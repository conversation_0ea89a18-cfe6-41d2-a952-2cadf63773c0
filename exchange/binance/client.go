package binance

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/sirupsen/logrus"

	"exchange_signal/config"
	"exchange_signal/data"
	"exchange_signal/exchange"
)

// BinanceClient Binance交易所客户端
type BinanceClient struct {
	*exchange.BaseExchangeClient

	// Binance特定配置
	logger *logrus.Logger

	// WebSocket管理
	tradeManager     *TradeDataManager
	orderbookManager *OrderbookDataManager

	// 连接控制
	wg sync.WaitGroup
}

// SymbolInfo 交易对信息
type SymbolInfo struct {
	Symbol string `json:"symbol"`
	Status string `json:"status"`
}

// ExchangeInfo 交易所信息
type ExchangeInfo struct {
	Symbols []SymbolInfo `json:"symbols"`
}

// NewBinanceClient 创建新的Binance客户端
func NewBinanceClient() *BinanceClient {
	baseClient := exchange.NewBaseExchangeClient("binance")

	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})

	client := &BinanceClient{
		BaseExchangeClient: baseClient,
		logger:             logger,
	}

	return client
}

// SetConfig 设置配置
func (bc *BinanceClient) SetConfig(cfg *config.ExchangeConfig) {
	bc.BaseExchangeClient.SetConfig(cfg)

	// 设置日志级别
	if level, err := logrus.ParseLevel("info"); err == nil {
		bc.logger.SetLevel(level)
	}
}

// GetSymbols 获取所有永续合约交易对
func (bc *BinanceClient) GetSymbols() ([]string, error) {
	bc.logger.Info("🔄 正在获取Binance永续合约交易对...")

	if bc.Config == nil {
		return nil, fmt.Errorf("配置未设置")
	}

	resp, err := http.Get(bc.Config.APIBaseURL + "/fapi/v1/exchangeInfo")
	if err != nil {
		return nil, fmt.Errorf("获取交易对信息失败: %v", err)
	}
	defer resp.Body.Close()

	var exchangeInfo ExchangeInfo
	if err := json.NewDecoder(resp.Body).Decode(&exchangeInfo); err != nil {
		return nil, fmt.Errorf("解析交易对信息失败: %v", err)
	}

	symbols := make([]string, 0)
	for _, symbol := range exchangeInfo.Symbols {
		if symbol.Status == "TRADING" {
			// 应用交易对过滤器
			if bc.shouldIncludeSymbol(symbol.Symbol) {
				symbols = append(symbols, symbol.Symbol)
			}
		}
	}

	bc.Symbols = symbols
	bc.logger.Infof("✅ 成功获取 %d 个Binance交易对", len(symbols))
	return symbols, nil
}

// shouldIncludeSymbol 检查是否应包含该交易对
func (bc *BinanceClient) shouldIncludeSymbol(symbol string) bool {
	if bc.Config == nil || len(bc.Config.SymbolFilter) == 0 {
		return true // 无过滤器，包含所有
	}

	for _, filter := range bc.Config.SymbolFilter {
		if symbol == filter {
			return true
		}
	}
	return false
}

// Connect 连接到Binance
func (bc *BinanceClient) Connect(ctx context.Context) error {
	bc.logger.Info("🔗 连接到Binance...")

	if bc.Config == nil {
		return fmt.Errorf("配置未设置")
	}

	// 获取交易对
	symbols, err := bc.GetSymbols()
	if err != nil {
		return fmt.Errorf("获取交易对失败: %v", err)
	}

	if len(symbols) == 0 {
		return fmt.Errorf("没有可用的交易对")
	}

	// 初始化数据管理器（简化版本）
	bc.tradeManager = &TradeDataManager{client: bc, symbols: symbols}
	bc.orderbookManager = &OrderbookDataManager{client: bc, symbols: symbols}

	bc.Status.State = "connected"
	bc.Status.ConnectedTime = time.Now()

	bc.logger.Info("✅ Binance连接成功")
	return nil
}

// Start 启动Binance客户端
func (bc *BinanceClient) Start() error {
	if err := bc.Connect(bc.Ctx); err != nil {
		return err
	}

	bc.Status.State = "connecting"
	bc.Stats.StartTime = time.Now()

	// 启动交易数据订阅
	if bc.Config.EnableTradeData {
		bc.wg.Add(1)
		go func() {
			defer bc.wg.Done()
			if err := bc.tradeManager.Start(); err != nil {
				bc.logger.Errorf("❌ 启动交易数据管理器失败: %v", err)
			}
		}()
	}

	// 启动盘口数据订阅
	if bc.Config.EnableOrderbookData {
		bc.wg.Add(1)
		go func() {
			defer bc.wg.Done()
			if err := bc.orderbookManager.Start(); err != nil {
				bc.logger.Errorf("❌ 启动盘口数据管理器失败: %v", err)
			}
		}()
	}

	bc.Status.State = "connected"
	bc.logger.Info("✅ Binance客户端启动成功")
	return nil
}

// Stop 停止Binance客户端
func (bc *BinanceClient) Stop() error {
	bc.logger.Info("🛑 停止Binance客户端...")

	// 停止数据管理器
	if bc.tradeManager != nil {
		bc.tradeManager.Stop()
	}
	if bc.orderbookManager != nil {
		bc.orderbookManager.Stop()
	}

	// 等待所有goroutine结束
	bc.wg.Wait()

	// 调用基础停止方法
	bc.BaseExchangeClient.Stop()

	bc.logger.Info("✅ Binance客户端停止完成")
	return nil
}

// SubscribeTradeData 订阅交易数据
func (bc *BinanceClient) SubscribeTradeData(symbols []string) error {
	if bc.tradeManager == nil {
		return fmt.Errorf("交易数据管理器未初始化")
	}
	return bc.tradeManager.SubscribeSymbols(symbols)
}

// SubscribeOrderbookData 订阅盘口数据
func (bc *BinanceClient) SubscribeOrderbookData(symbols []string) error {
	if bc.orderbookManager == nil {
		return fmt.Errorf("盘口数据管理器未初始化")
	}
	return bc.orderbookManager.SubscribeSymbols(symbols)
}

// SubscribeDepthData 订阅深度数据（暂不实现）
func (bc *BinanceClient) SubscribeDepthData(symbols []string) error {
	// Binance深度数据订阅，这里暂不实现
	return nil
}

// SubscribeFundingData 订阅资金费率数据（暂不实现）
func (bc *BinanceClient) SubscribeFundingData(symbols []string) error {
	// Binance资金费率数据订阅，这里暂不实现
	return nil
}

// StartHeartbeat 启动心跳
func (bc *BinanceClient) StartHeartbeat() error {
	// 心跳由各个数据管理器处理
	return nil
}

// StopHeartbeat 停止心跳
func (bc *BinanceClient) StopHeartbeat() error {
	// 心跳由各个数据管理器处理
	return nil
}

// handleTradeData 处理交易数据
func (bc *BinanceClient) handleTradeData(rawData []byte) error {
	// 解析Binance交易数据
	var binanceData struct {
		EventType string `json:"e"`
		EventTime int64  `json:"E"`
		Symbol    string `json:"s"`
		TradeID   int64  `json:"t"`
		Price     string `json:"p"`
		Quantity  string `json:"q"`
		BuyerID   int64  `json:"b"`
		SellerID  int64  `json:"a"`
		TradeTime int64  `json:"T"`
		IsBuyer   bool   `json:"m"`
	}

	if err := json.Unmarshal(rawData, &binanceData); err != nil {
		return fmt.Errorf("解析交易数据失败: %v", err)
	}

	// 转换为统一格式
	tradeData := &data.TradeData{
		Exchange:  "binance",
		Symbol:    binanceData.Symbol,
		TradeID:   fmt.Sprintf("%d", binanceData.TradeID),
		Price:     binanceData.Price,
		Quantity:  binanceData.Quantity,
		Side:      getBinanceTradeSide(binanceData.IsBuyer),
		TradeTime: binanceData.TradeTime,
		IsBuyerMM: binanceData.IsBuyer,
		Timestamp: time.Now(),
	}

	// 更新统计
	bc.Stats.TradeCount++
	bc.Stats.LastDataTime = time.Now()

	// 发送到数据处理器
	if bc.DataHandler != nil {
		return bc.DataHandler.HandleTradeData(tradeData)
	}

	return nil
}

// handleOrderbookData 处理盘口数据
func (bc *BinanceClient) handleOrderbookData(rawData []byte) error {
	// 解析Binance盘口数据
	var binanceData struct {
		UpdateID     int64  `json:"u"`
		Symbol       string `json:"s"`
		BestBidPrice string `json:"b"`
		BestBidQty   string `json:"B"`
		BestAskPrice string `json:"a"`
		BestAskQty   string `json:"A"`
	}

	if err := json.Unmarshal(rawData, &binanceData); err != nil {
		return fmt.Errorf("解析盘口数据失败: %v", err)
	}

	// 转换为统一格式
	orderbookData := &data.OrderbookData{
		Exchange:     "binance",
		Symbol:       binanceData.Symbol,
		BestBidPrice: binanceData.BestBidPrice,
		BestBidQty:   binanceData.BestBidQty,
		BestAskPrice: binanceData.BestAskPrice,
		BestAskQty:   binanceData.BestAskQty,
		UpdateID:     binanceData.UpdateID,
		Timestamp:    time.Now(),
	}

	// 更新统计
	bc.Stats.OrderbookCount++
	bc.Stats.LastDataTime = time.Now()

	// 发送到数据处理器
	if bc.DataHandler != nil {
		return bc.DataHandler.HandleOrderbookData(orderbookData)
	}

	return nil
}

// getBinanceTradeSide 获取交易方向
func getBinanceTradeSide(isBuyer bool) string {
	if isBuyer {
		return "buy"
	}
	return "sell"
}
