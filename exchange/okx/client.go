package okx

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/sirupsen/logrus"

	"exchange_signal/config"
	"exchange_signal/exchange"
)

// OKXClient OKX交易所客户端
type OKXClient struct {
	*exchange.BaseExchangeClient

	// OKX特定配置
	logger *logrus.Logger

	// WebSocket管理
	tradeManager     *OKXTradeDataManager
	orderbookManager *OKXOrderbookDataManager

	// 连接控制
	wg sync.WaitGroup
}

// SymbolInfo OKX交易对信息
type OKXSymbolInfo struct {
	InstID   string `json:"instId"`
	InstType string `json:"instType"`
	State    string `json:"state"`
}

// InstrumentsResponse OKX交易对响应
type OKXInstrumentsResponse struct {
	Code string          `json:"code"`
	Msg  string          `json:"msg"`
	Data []OKXSymbolInfo `json:"data"`
}

// NewOKXClient 创建新的OKX客户端
func NewOKXClient() *OKXClient {
	baseClient := exchange.NewBaseExchangeClient("okx")

	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})

	client := &OKXClient{
		BaseExchangeClient: baseClient,
		logger:             logger,
	}

	return client
}

// SetConfig 设置配置
func (oc *OKXClient) SetConfig(cfg *config.ExchangeConfig) {
	oc.BaseExchangeClient.SetConfig(cfg)

	// 设置日志级别
	if level, err := logrus.ParseLevel("info"); err == nil {
		oc.logger.SetLevel(level)
	}
}

// GetSymbols 获取所有永续合约交易对
func (oc *OKXClient) GetSymbols() ([]string, error) {
	oc.logger.Info("🔄 正在获取OKX永续合约交易对...")

	if oc.Config == nil {
		return nil, fmt.Errorf("配置未设置")
	}

	resp, err := http.Get(oc.Config.APIBaseURL + "/api/v5/public/instruments?instType=SWAP")
	if err != nil {
		return nil, fmt.Errorf("获取交易对信息失败: %v", err)
	}
	defer resp.Body.Close()

	var instrumentsResp OKXInstrumentsResponse
	if err := json.NewDecoder(resp.Body).Decode(&instrumentsResp); err != nil {
		return nil, fmt.Errorf("解析交易对信息失败: %v", err)
	}

	if instrumentsResp.Code != "0" {
		return nil, fmt.Errorf("OKX API错误: %s", instrumentsResp.Msg)
	}

	symbols := make([]string, 0)
	for _, symbol := range instrumentsResp.Data {
		if symbol.State == "live" {
			// 应用交易对过滤器
			if oc.shouldIncludeSymbol(symbol.InstID) {
				symbols = append(symbols, symbol.InstID)
			}
		}
	}

	oc.Symbols = symbols
	oc.logger.Infof("✅ 成功获取 %d 个OKX交易对", len(symbols))
	return symbols, nil
}

// shouldIncludeSymbol 检查是否应包含该交易对
func (oc *OKXClient) shouldIncludeSymbol(symbol string) bool {
	if oc.Config == nil || len(oc.Config.SymbolFilter) == 0 {
		return true // 无过滤器，包含所有
	}

	for _, filter := range oc.Config.SymbolFilter {
		if symbol == filter {
			return true
		}
	}
	return false
}

// Connect 连接到OKX
func (oc *OKXClient) Connect(ctx context.Context) error {
	oc.logger.Info("🔗 连接到OKX...")

	if oc.Config == nil {
		return fmt.Errorf("配置未设置")
	}

	// 获取交易对
	symbols, err := oc.GetSymbols()
	if err != nil {
		return fmt.Errorf("获取交易对失败: %v", err)
	}

	if len(symbols) == 0 {
		return fmt.Errorf("没有可用的交易对")
	}

	// 初始化数据管理器
	oc.tradeManager = &OKXTradeDataManager{client: oc, symbols: symbols}
	oc.orderbookManager = &OKXOrderbookDataManager{client: oc, symbols: symbols}

	oc.Status.State = "connected"
	oc.Status.ConnectedTime = time.Now()

	oc.logger.Info("✅ OKX连接成功")
	return nil
}

// Start 启动OKX客户端
func (oc *OKXClient) Start() error {
	if err := oc.Connect(oc.Ctx); err != nil {
		return err
	}

	oc.Status.State = "connecting"
	oc.Stats.StartTime = time.Now()

	// 启动交易数据订阅
	if oc.Config.EnableTradeData {
		oc.wg.Add(1)
		go func() {
			defer oc.wg.Done()
			if err := oc.tradeManager.Start(); err != nil {
				oc.logger.Errorf("❌ 启动交易数据管理器失败: %v", err)
			}
		}()
	}

	// 启动盘口数据订阅
	if oc.Config.EnableOrderbookData {
		oc.wg.Add(1)
		go func() {
			defer oc.wg.Done()
			if err := oc.orderbookManager.Start(); err != nil {
				oc.logger.Errorf("❌ 启动盘口数据管理器失败: %v", err)
			}
		}()
	}

	oc.Status.State = "connected"
	oc.logger.Info("✅ OKX客户端启动成功")
	return nil
}

// Stop 停止OKX客户端
func (oc *OKXClient) Stop() error {
	oc.logger.Info("🛑 停止OKX客户端...")

	// 停止数据管理器
	if oc.tradeManager != nil {
		oc.tradeManager.Stop()
	}
	if oc.orderbookManager != nil {
		oc.orderbookManager.Stop()
	}

	// 等待所有goroutine结束
	oc.wg.Wait()

	// 调用基础停止方法
	oc.BaseExchangeClient.Stop()

	oc.logger.Info("✅ OKX客户端停止完成")
	return nil
}

// SubscribeTradeData 订阅交易数据
func (oc *OKXClient) SubscribeTradeData(symbols []string) error {
	if oc.tradeManager == nil {
		return fmt.Errorf("交易数据管理器未初始化")
	}
	return oc.tradeManager.SubscribeSymbols(symbols)
}

// SubscribeOrderbookData 订阅盘口数据
func (oc *OKXClient) SubscribeOrderbookData(symbols []string) error {
	if oc.orderbookManager == nil {
		return fmt.Errorf("盘口数据管理器未初始化")
	}
	return oc.orderbookManager.SubscribeSymbols(symbols)
}

// SubscribeDepthData 订阅深度数据（暂不实现）
func (oc *OKXClient) SubscribeDepthData(symbols []string) error {
	// OKX深度数据订阅，这里暂不实现
	return nil
}

// SubscribeFundingData 订阅资金费率数据（暂不实现）
func (oc *OKXClient) SubscribeFundingData(symbols []string) error {
	// OKX资金费率数据订阅，这里暂不实现
	return nil
}

// StartHeartbeat 启动心跳
func (oc *OKXClient) StartHeartbeat() error {
	// 心跳由各个数据管理器处理
	return nil
}

// StopHeartbeat 停止心跳
func (oc *OKXClient) StopHeartbeat() error {
	// 心跳由各个数据管理器处理
	return nil
}
