package okx

import (
	"context"
	"encoding/json"
	"exchange_signal/data"
	"fmt"
	"net/url"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gorilla/websocket"
)

// WebSocket配置
const (
	OKXWSURL                = "wss://ws.okx.com:8443/ws/v5/public"
	OKXPingInterval         = 25 * time.Second
	OKXPongTimeout          = 150 * time.Second
	OKXReconnectDelay       = 3 * time.Second
	OKXMaxReconnectAttempts = 10
)

// 连接状态
type OKXConnectionState int

const (
	OKXDisconnected OKXConnectionState = iota
	OKXConnecting
	OKXConnected
	OKXReconnecting
	OKXStopped
)

// WebSocket连接管理器
type OKXWSConnection struct {
	conn           *websocket.Conn
	connMutex      sync.RWMutex
	state          OKXConnectionState
	stateMutex     sync.RWMutex
	lastPong       time.Time
	pingTicker     *time.Ticker
	reconnectCount int
}

// TradeDataManager 交易数据管理器
type OKXTradeDataManager struct {
	client      *OKXClient
	symbols     []string
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
	connections map[string]*OKXWSConnection
	connMutex   sync.RWMutex
	tradeCount  int64
}

// OrderbookDataManager 盘口数据管理器
type OKXOrderbookDataManager struct {
	client         *OKXClient
	symbols        []string
	ctx            context.Context
	cancel         context.CancelFunc
	wg             sync.WaitGroup
	connections    map[string]*OKXWSConnection
	connMutex      sync.RWMutex
	orderbookCount int64
}

// OKX订阅消息结构
type OKXSubscriptionMsg struct {
	Op   string                   `json:"op"`
	Args []map[string]interface{} `json:"args"`
}

// OKX交易数据结构
type OKXTradeData struct {
	Arg struct {
		Channel string `json:"channel"`
		InstID  string `json:"instId"`
	} `json:"arg"`
	Data []struct {
		InstID    string `json:"instId"`
		TradeID   string `json:"tradeId"`
		Price     string `json:"px"`
		Size      string `json:"sz"`
		Side      string `json:"side"`
		Timestamp string `json:"ts"`
	} `json:"data"`
}

// OKX盘口数据结构
type OKXBookTickerData struct {
	Arg struct {
		Channel string `json:"channel"`
		InstID  string `json:"instId"`
	} `json:"arg"`
	Data []struct {
		InstID    string     `json:"instId"`
		Asks      [][]string `json:"asks"`
		Bids      [][]string `json:"bids"`
		Timestamp string     `json:"ts"`
	} `json:"data"`
}

// 设置连接状态
func (ws *OKXWSConnection) setState(state OKXConnectionState) {
	ws.stateMutex.Lock()
	defer ws.stateMutex.Unlock()
	ws.state = state
}

// 获取连接状态
func (ws *OKXWSConnection) getState() OKXConnectionState {
	ws.stateMutex.RLock()
	defer ws.stateMutex.RUnlock()
	return ws.state
}

// Start 启动交易数据管理器
func (tm *OKXTradeDataManager) Start() error {
	tm.ctx, tm.cancel = context.WithCancel(context.Background())
	tm.connections = make(map[string]*OKXWSConnection)

	tm.client.logger.Info("🚀 启动OKX交易数据订阅...")

	// 按批次分组订阅
	batchSize := tm.client.Config.BatchSize
	if batchSize <= 0 {
		batchSize = 20
	}

	for i := 0; i < len(tm.symbols); i += batchSize {
		end := i + batchSize
		if end > len(tm.symbols) {
			end = len(tm.symbols)
		}
		batch := tm.symbols[i:end]
		connName := fmt.Sprintf("okx_trade_batch_%d", i/batchSize+1)

		tm.wg.Add(1)
		go tm.subscribeTradeData(connName, batch)
	}

	return nil
}

// 订阅交易数据
func (tm *OKXTradeDataManager) subscribeTradeData(connName string, symbols []string) {
	defer tm.wg.Done()

	// 构建订阅参数
	var args []map[string]interface{}
	for _, symbol := range symbols {
		args = append(args, map[string]interface{}{
			"channel": "trades",
			"instId":  symbol,
		})
	}

	// 带重连的订阅
	tm.subscribeWithRetry(connName, args, tm.handleTradeMessage)
}

// 带重连的订阅
func (tm *OKXTradeDataManager) subscribeWithRetry(connName string, args []map[string]interface{}, handler func([]byte)) {
	wsConn := &OKXWSConnection{
		lastPong: time.Now(),
	}

	tm.connMutex.Lock()
	tm.connections[connName] = wsConn
	tm.connMutex.Unlock()

	reconnectAttempts := 0
	currentDelay := OKXReconnectDelay

	for {
		select {
		case <-tm.ctx.Done():
			tm.client.logger.Infof("🛑 OKX连接 %s 收到停止信号，停止重连", connName)
			return
		default:
			if tm.connectAndListen(connName, args, handler, wsConn, &reconnectAttempts) {
				return // 正常退出
			}

			// 检查是否已经停止
			select {
			case <-tm.ctx.Done():
				tm.client.logger.Infof("🛑 OKX连接 %s 收到停止信号，停止重连", connName)
				return
			default:
			}

			// 重连逻辑
			reconnectAttempts++
			if reconnectAttempts >= OKXMaxReconnectAttempts {
				tm.client.logger.Errorf("❌ OKX连接 %s 重连次数超过限制", connName)
				return
			}

			wsConn.setState(OKXReconnecting)
			tm.client.logger.Warnf("🔄 OKX连接 %s 准备重连 (第%d次)，等待 %v", connName, reconnectAttempts, currentDelay)

			// 可中断的等待
			select {
			case <-tm.ctx.Done():
				tm.client.logger.Infof("🛑 OKX连接 %s 在等待重连时收到停止信号", connName)
				return
			case <-time.After(currentDelay):
				// 继续重连
			}

			// 指数退避
			if currentDelay < 300*time.Second {
				currentDelay *= 2
			}
		}
	}
}

// 连接并监听
func (tm *OKXTradeDataManager) connectAndListen(connName string, args []map[string]interface{}, handler func([]byte), wsConn *OKXWSConnection, reconnectAttempts *int) bool {
	wsConn.setState(OKXConnecting)

	// 建立WebSocket连接
	u, _ := url.Parse(OKXWSURL)
	dialer := websocket.DefaultDialer
	dialer.HandshakeTimeout = 10 * time.Second

	conn, _, err := dialer.Dial(u.String(), nil)
	if err != nil {
		tm.client.logger.Errorf("❌ OKX WebSocket连接失败 %s: %v", connName, err)
		return false
	}

	wsConn.connMutex.Lock()
	wsConn.conn = conn
	wsConn.connMutex.Unlock()
	wsConn.setState(OKXConnected)

	tm.client.logger.Info("✅ OKX WebSocket连接成功 %s", connName)

	// 发送订阅消息
	subMsg := OKXSubscriptionMsg{
		Op:   "subscribe",
		Args: args,
	}

	if err := conn.WriteJSON(subMsg); err != nil {
		tm.client.logger.Errorf("❌ OKX发送订阅消息失败 %s: %v", connName, err)
		conn.Close()
		return false
	}

	// 启动心跳
	wsConn.pingTicker = time.NewTicker(OKXPingInterval)
	go tm.startPing(conn, connName, wsConn)

	// 监听消息
	for {
		select {
		case <-tm.ctx.Done():
			wsConn.setState(OKXStopped)
			conn.Close()
			return true
		default:
			_, message, err := conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure, websocket.CloseNormalClosure) {
					tm.client.logger.Errorf("❌ OKX WebSocket异常关闭 %s: %v", connName, err)
				} else {
					tm.client.logger.Warnf("🔌 OKX WebSocket连接关闭 %s: %v", connName, err)
				}
				wsConn.setState(OKXDisconnected)
				conn.Close()
				if wsConn.pingTicker != nil {
					wsConn.pingTicker.Stop()
				}
				return false
			}

			// 检查pong超时
			if time.Since(wsConn.lastPong) > OKXPongTimeout {
				tm.client.logger.Errorf("💔 OKX Pong超时 %s", connName)
				wsConn.setState(OKXDisconnected)
				conn.Close()
				if wsConn.pingTicker != nil {
					wsConn.pingTicker.Stop()
				}
				return false
			}

			handler(message)
		}
	}
}

// 启动心跳 (OKX使用文本ping)
func (tm *OKXTradeDataManager) startPing(conn *websocket.Conn, connName string, wsConn *OKXWSConnection) {
	defer wsConn.pingTicker.Stop()

	for {
		select {
		case <-tm.ctx.Done():
			return
		case <-wsConn.pingTicker.C:
			if wsConn.getState() != OKXConnected {
				return
			}

			wsConn.connMutex.Lock()
			if wsConn.conn != nil {
				// OKX使用文本ping
				err := wsConn.conn.WriteMessage(websocket.TextMessage, []byte("ping"))
				if err != nil {
					tm.client.logger.Errorf("💔 OKX发送心跳失败 %s: %v", connName, err)
					wsConn.connMutex.Unlock()
					return
				}
				wsConn.lastPong = time.Now() // OKX会自动回复pong
			}
			wsConn.connMutex.Unlock()
		}
	}
}

// 处理交易消息
func (tm *OKXTradeDataManager) handleTradeMessage(message []byte) {
	// 检查是否是pong消息
	if string(message) == "pong" {
		return
	}

	// 检查是否是订阅确认（静默处理）
	var result map[string]interface{}
	if err := json.Unmarshal(message, &result); err == nil {
		if event, ok := result["event"]; ok && event == "subscribe" {
			return
		}
	}

	// 解析交易数据
	var tradeData OKXTradeData
	if err := json.Unmarshal(message, &tradeData); err != nil {
		return // 忽略无法解析的消息
	}

	if len(tradeData.Data) > 0 && tradeData.Arg.Channel == "trades" {
		for _, trade := range tradeData.Data {
			atomic.AddInt64(&tm.tradeCount, 1)

			// 转换时间戳
			ts, _ := strconv.ParseInt(trade.Timestamp, 10, 64)

			// 处理数据
			tradeDataStruct := &data.TradeData{
				Exchange:  tm.client.Name,
				Symbol:    trade.InstID,
				TradeID:   trade.TradeID,
				Price:     trade.Price,
				Quantity:  trade.Size,
				Side:      trade.Side,
				TradeTime: ts,
				Timestamp: time.Now().UTC(),
			}
			tm.client.DataHandler.HandleTradeData(tradeDataStruct)
		}
	}
}

// Stop 停止交易数据管理器
func (tm *OKXTradeDataManager) Stop() {
	if tm.cancel != nil {
		tm.cancel()
	}

	// 关闭所有连接
	tm.connMutex.Lock()
	for _, wsConn := range tm.connections {
		wsConn.setState(OKXStopped)
		if wsConn.pingTicker != nil {
			wsConn.pingTicker.Stop()
		}
		wsConn.connMutex.Lock()
		if wsConn.conn != nil {
			wsConn.conn.Close()
		}
		wsConn.connMutex.Unlock()
	}
	tm.connMutex.Unlock()

	tm.wg.Wait()
}

// SubscribeSymbols 订阅交易对
func (tm *OKXTradeDataManager) SubscribeSymbols(symbols []string) error {
	tm.symbols = symbols
	return nil
}

// GetTradeCount 获取交易数据计数
func (tm *OKXTradeDataManager) GetTradeCount() int64 {
	return atomic.LoadInt64(&tm.tradeCount)
}

// Start 启动盘口数据管理器
func (om *OKXOrderbookDataManager) Start() error {
	om.ctx, om.cancel = context.WithCancel(context.Background())
	om.connections = make(map[string]*OKXWSConnection)

	om.client.logger.Info("🚀 启动OKX盘口数据订阅...")

	// 按批次分组订阅
	batchSize := om.client.Config.BatchSize
	if batchSize <= 0 {
		batchSize = 20
	}

	for i := 0; i < len(om.symbols); i += batchSize {
		end := i + batchSize
		if end > len(om.symbols) {
			end = len(om.symbols)
		}
		batch := om.symbols[i:end]
		connName := fmt.Sprintf("okx_orderbook_batch_%d", i/batchSize+1)

		om.wg.Add(1)
		go om.subscribeOrderbookData(connName, batch)
	}

	return nil
}

// 订阅盘口数据
func (om *OKXOrderbookDataManager) subscribeOrderbookData(connName string, symbols []string) {
	defer om.wg.Done()

	// 构建订阅参数
	var args []map[string]interface{}
	for _, symbol := range symbols {
		args = append(args, map[string]interface{}{
			"channel": "books",
			"instId":  symbol,
		})
	}

	// 带重连的订阅
	om.subscribeWithRetry(connName, args, om.handleOrderbookMessage)
}

// 带重连的订阅（复用交易数据的逻辑，但使用不同的处理器）
func (om *OKXOrderbookDataManager) subscribeWithRetry(connName string, args []map[string]interface{}, handler func([]byte)) {
	wsConn := &OKXWSConnection{
		lastPong: time.Now(),
	}

	om.connMutex.Lock()
	om.connections[connName] = wsConn
	om.connMutex.Unlock()

	reconnectAttempts := 0
	currentDelay := OKXReconnectDelay

	for {
		select {
		case <-om.ctx.Done():
			return
		default:
			if om.connectAndListen(connName, args, handler, wsConn, &reconnectAttempts) {
				return // 正常退出
			}

			// 重连逻辑
			reconnectAttempts++
			if reconnectAttempts >= OKXMaxReconnectAttempts {
				om.client.logger.Error("❌ OKX连接 %s 重连次数超过限制", connName)
				return
			}

			wsConn.setState(OKXReconnecting)
			om.client.logger.Warn("🔄 OKX连接 %s 准备重连 (第%d次)，等待 %v", connName, reconnectAttempts, currentDelay)
			time.Sleep(currentDelay)

			// 指数退避
			if currentDelay < 300*time.Second {
				currentDelay *= 2
			}
		}
	}
}

// 连接并监听（盘口数据）
func (om *OKXOrderbookDataManager) connectAndListen(connName string, args []map[string]interface{}, handler func([]byte), wsConn *OKXWSConnection, reconnectAttempts *int) bool {
	wsConn.setState(OKXConnecting)

	// 建立WebSocket连接
	u, _ := url.Parse(OKXWSURL)
	dialer := websocket.DefaultDialer
	dialer.HandshakeTimeout = 10 * time.Second

	conn, _, err := dialer.Dial(u.String(), nil)
	if err != nil {
		om.client.logger.Error("❌ OKX WebSocket连接失败 %s: %v", connName, err)
		return false
	}

	wsConn.connMutex.Lock()
	wsConn.conn = conn
	wsConn.connMutex.Unlock()
	wsConn.setState(OKXConnected)

	om.client.logger.Info("✅ OKX WebSocket连接成功 %s", connName)

	// 发送订阅消息
	subMsg := OKXSubscriptionMsg{
		Op:   "subscribe",
		Args: args,
	}

	if err := conn.WriteJSON(subMsg); err != nil {
		om.client.logger.Error("❌ OKX发送订阅消息失败 %s: %v", connName, err)
		conn.Close()
		return false
	}

	// 启动心跳
	wsConn.pingTicker = time.NewTicker(OKXPingInterval)
	go om.startPing(conn, connName, wsConn)

	// 监听消息
	for {
		select {
		case <-om.ctx.Done():
			wsConn.setState(OKXStopped)
			conn.Close()
			return true
		default:
			_, message, err := conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure, websocket.CloseNormalClosure) {
					om.client.logger.Error("❌ OKX WebSocket异常关闭 %s: %v", connName, err)
				} else {
					om.client.logger.Warn("🔌 OKX WebSocket连接关闭 %s: %v", connName, err)
				}
				wsConn.setState(OKXDisconnected)
				conn.Close()
				if wsConn.pingTicker != nil {
					wsConn.pingTicker.Stop()
				}
				return false
			}

			// 检查pong超时
			if time.Since(wsConn.lastPong) > OKXPongTimeout {
				om.client.logger.Error("💔 OKX Pong超时 %s", connName)
				wsConn.setState(OKXDisconnected)
				conn.Close()
				if wsConn.pingTicker != nil {
					wsConn.pingTicker.Stop()
				}
				return false
			}

			handler(message)
		}
	}
}

// 启动心跳 (盘口数据)
func (om *OKXOrderbookDataManager) startPing(conn *websocket.Conn, connName string, wsConn *OKXWSConnection) {
	defer wsConn.pingTicker.Stop()

	for {
		select {
		case <-om.ctx.Done():
			return
		case <-wsConn.pingTicker.C:
			if wsConn.getState() != OKXConnected {
				return
			}

			wsConn.connMutex.Lock()
			if wsConn.conn != nil {
				// OKX使用文本ping
				err := wsConn.conn.WriteMessage(websocket.TextMessage, []byte("ping"))
				if err != nil {
					om.client.logger.Error("💔 OKX发送心跳失败 %s: %v", connName, err)
					wsConn.connMutex.Unlock()
					return
				}
				wsConn.lastPong = time.Now() // OKX会自动回复pong
			}
			wsConn.connMutex.Unlock()
		}
	}
}

// 处理盘口消息
func (om *OKXOrderbookDataManager) handleOrderbookMessage(message []byte) {
	// 检查是否是pong消息
	if string(message) == "pong" {
		return
	}

	// 检查是否是订阅确认（静默处理）
	var result map[string]interface{}
	if err := json.Unmarshal(message, &result); err == nil {
		if event, ok := result["event"]; ok && event == "subscribe" {
			return
		}
	}

	// 解析盘口数据
	var bookData OKXBookTickerData
	if err := json.Unmarshal(message, &bookData); err != nil {
		return // 忽略无法解析的消息
	}

	if len(bookData.Data) > 0 && bookData.Arg.Channel == "books" {
		for _, book := range bookData.Data {
			atomic.AddInt64(&om.orderbookCount, 1)

			// 获取最佳买卖价
			var bestBidPrice, bestBidQty, bestAskPrice, bestAskQty string
			if len(book.Bids) > 0 && len(book.Bids[0]) >= 2 {
				bestBidPrice = book.Bids[0][0]
				bestBidQty = book.Bids[0][1]
			}
			if len(book.Asks) > 0 && len(book.Asks[0]) >= 2 {
				bestAskPrice = book.Asks[0][0]
				bestAskQty = book.Asks[0][1]
			}

			// 转换时间戳
			ts, _ := strconv.ParseInt(book.Timestamp, 10, 64)

			// 处理数据
			orderbookDataStruct := &data.OrderbookData{
				Exchange:     om.client.Name,
				Symbol:       book.InstID,
				BestBidPrice: bestBidPrice,
				BestBidQty:   bestBidQty,
				BestAskPrice: bestAskPrice,
				BestAskQty:   bestAskQty,
				UpdateID:     ts,
				Timestamp:    time.Now().UTC(),
			}
			om.client.DataHandler.HandleOrderbookData(orderbookDataStruct)
		}
	}
}

// Stop 停止盘口数据管理器
func (om *OKXOrderbookDataManager) Stop() {
	if om.cancel != nil {
		om.cancel()
	}

	// 关闭所有连接
	om.connMutex.Lock()
	for _, wsConn := range om.connections {
		wsConn.setState(OKXStopped)
		if wsConn.pingTicker != nil {
			wsConn.pingTicker.Stop()
		}
		wsConn.connMutex.Lock()
		if wsConn.conn != nil {
			wsConn.conn.Close()
		}
		wsConn.connMutex.Unlock()
	}
	om.connMutex.Unlock()

	om.wg.Wait()
}

// SubscribeSymbols 订阅交易对
func (om *OKXOrderbookDataManager) SubscribeSymbols(symbols []string) error {
	om.symbols = symbols
	return nil
}

// GetOrderbookCount 获取盘口数据计数
func (om *OKXOrderbookDataManager) GetOrderbookCount() int64 {
	return atomic.LoadInt64(&om.orderbookCount)
}
