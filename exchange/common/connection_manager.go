package common

import (
	"context"
	"fmt"
	"math"
	"sync"
	"sync/atomic"
	"time"

	"github.com/sirupsen/logrus"
)

// ReconnectStrategy 重连策略类型
type ReconnectStrategy string

const (
	ExponentialBackoff         ReconnectStrategy = "exponential_backoff"
	FastExponentialBackoff     ReconnectStrategy = "fast_exponential_backoff"
	CautiousExponentialBackoff ReconnectStrategy = "cautious_exponential_backoff"
	LinearBackoff              ReconnectStrategy = "linear_backoff"
	FixedDelay                 ReconnectStrategy = "fixed_delay"
)

// ConnectionState 连接状态
type ConnectionState int

const (
	Disconnected ConnectionState = iota
	Connecting
	Connected
	Reconnecting
	Stopped
	Error
)

func (cs ConnectionState) String() string {
	switch cs {
	case Disconnected:
		return "disconnected"
	case Connecting:
		return "connecting"
	case Connected:
		return "connected"
	case Reconnecting:
		return "reconnecting"
	case Stopped:
		return "stopped"
	case Error:
		return "error"
	default:
		return "unknown"
	}
}

// StabilityConfig 稳定性配置
type StabilityConfig struct {
	MaxConnectionsPerIP           int           `json:"max_connections_per_ip"`
	MaxSubscriptionsPerConnection int           `json:"max_subscriptions_per_connection"`
	RateLimitPerMinute            int           `json:"rate_limit_per_minute"`
	ConnectionHealthCheckInterval time.Duration `json:"connection_health_check_interval"`
	AggressiveReconnect           bool          `json:"aggressive_reconnect"`
	ErrorThreshold                int           `json:"error_threshold"`
	ConservativeMode              bool          `json:"conservative_mode"`
	CautiousMode                  bool          `json:"cautious_mode"`
	PeakHourBatchReduction        float64       `json:"peak_hour_batch_reduction"`
	PeakHourHandling              string        `json:"peak_hour_handling"`
	StartupDelay                  time.Duration `json:"startup_delay"`
}

// ConnectionManager 连接管理器
type ConnectionManager struct {
	exchangeName     string
	strategy         ReconnectStrategy
	stabilityConfig  *StabilityConfig
	logger           *logrus.Logger
	
	// 重连参数
	baseDelay        time.Duration
	maxDelay         time.Duration
	maxAttempts      int
	backoffMultiplier float64
	
	// 状态管理
	state            ConnectionState
	stateMutex       sync.RWMutex
	errorCount       int64
	successCount     int64
	lastConnectTime  time.Time
	lastErrorTime    time.Time
	
	// 健康检查
	healthCheckTicker *time.Ticker
	healthCheckStop   chan struct{}
	
	// 连接质量监控
	connectionQuality float64
	qualityMutex      sync.RWMutex
}

// NewConnectionManager 创建新的连接管理器
func NewConnectionManager(exchangeName string, strategy ReconnectStrategy, stabilityConfig *StabilityConfig, logger *logrus.Logger) *ConnectionManager {
	cm := &ConnectionManager{
		exchangeName:      exchangeName,
		strategy:          strategy,
		stabilityConfig:   stabilityConfig,
		logger:            logger,
		state:             Disconnected,
		connectionQuality: 1.0,
		healthCheckStop:   make(chan struct{}),
	}
	
	// 设置重连参数
	cm.setReconnectParameters()
	
	// 启动健康检查
	if stabilityConfig != nil && stabilityConfig.ConnectionHealthCheckInterval > 0 {
		cm.startHealthCheck()
	}
	
	return cm
}

// setReconnectParameters 根据策略设置重连参数
func (cm *ConnectionManager) setReconnectParameters() {
	switch cm.strategy {
	case ExponentialBackoff:
		cm.baseDelay = 3 * time.Second
		cm.maxDelay = 300 * time.Second
		cm.maxAttempts = 12
		cm.backoffMultiplier = 2.0
		
	case FastExponentialBackoff:
		cm.baseDelay = 1 * time.Second
		cm.maxDelay = 90 * time.Second
		cm.maxAttempts = 15
		cm.backoffMultiplier = 1.8
		
	case CautiousExponentialBackoff:
		cm.baseDelay = 8 * time.Second
		cm.maxDelay = 600 * time.Second
		cm.maxAttempts = 8
		cm.backoffMultiplier = 2.5
		
	case LinearBackoff:
		cm.baseDelay = 5 * time.Second
		cm.maxDelay = 120 * time.Second
		cm.maxAttempts = 10
		cm.backoffMultiplier = 1.0 // 线性增长
		
	case FixedDelay:
		cm.baseDelay = 5 * time.Second
		cm.maxDelay = 5 * time.Second
		cm.maxAttempts = 10
		cm.backoffMultiplier = 1.0
		
	default:
		// 默认策略
		cm.baseDelay = 3 * time.Second
		cm.maxDelay = 300 * time.Second
		cm.maxAttempts = 10
		cm.backoffMultiplier = 2.0
	}
}

// GetReconnectDelay 计算重连延迟
func (cm *ConnectionManager) GetReconnectDelay(attempt int) time.Duration {
	if attempt <= 0 {
		return cm.baseDelay
	}
	
	var delay time.Duration
	
	switch cm.strategy {
	case ExponentialBackoff, FastExponentialBackoff, CautiousExponentialBackoff:
		// 指数退避
		delay = time.Duration(float64(cm.baseDelay) * math.Pow(cm.backoffMultiplier, float64(attempt-1)))
		
	case LinearBackoff:
		// 线性退避
		delay = cm.baseDelay + time.Duration(attempt-1)*cm.baseDelay
		
	case FixedDelay:
		// 固定延迟
		delay = cm.baseDelay
		
	default:
		delay = cm.baseDelay
	}
	
	// 限制最大延迟
	if delay > cm.maxDelay {
		delay = cm.maxDelay
	}
	
	// 根据连接质量调整延迟
	qualityFactor := cm.getConnectionQuality()
	if qualityFactor < 0.5 {
		// 连接质量差，增加延迟
		delay = time.Duration(float64(delay) * (2.0 - qualityFactor))
	}
	
	return delay
}

// ShouldReconnect 判断是否应该重连
func (cm *ConnectionManager) ShouldReconnect(attempt int) bool {
	// 检查最大重连次数
	if attempt >= cm.maxAttempts {
		return false
	}
	
	// 检查错误阈值
	if cm.stabilityConfig != nil && cm.stabilityConfig.ErrorThreshold > 0 {
		errorCount := atomic.LoadInt64(&cm.errorCount)
		if errorCount >= int64(cm.stabilityConfig.ErrorThreshold) {
			// 如果是保守模式，停止重连
			if cm.stabilityConfig.ConservativeMode || cm.stabilityConfig.CautiousMode {
				cm.logger.Warn("🛑 [%s] 错误次数超过阈值，保守模式下停止重连", cm.exchangeName)
				return false
			}
		}
	}
	
	// 检查是否在高峰期需要特殊处理
	if cm.isPeakHour() && cm.stabilityConfig != nil {
		if cm.stabilityConfig.PeakHourHandling == "stop_reconnect" {
			cm.logger.Warn("⏰ [%s] 高峰期停止重连", cm.exchangeName)
			return false
		}
	}
	
	return true
}

// SetState 设置连接状态
func (cm *ConnectionManager) SetState(state ConnectionState) {
	cm.stateMutex.Lock()
	defer cm.stateMutex.Unlock()
	
	oldState := cm.state
	cm.state = state
	
	if state == Connected {
		cm.lastConnectTime = time.Now()
		atomic.AddInt64(&cm.successCount, 1)
		// 重置错误计数
		atomic.StoreInt64(&cm.errorCount, 0)
	} else if state == Error || state == Disconnected {
		cm.lastErrorTime = time.Now()
		atomic.AddInt64(&cm.errorCount, 1)
	}
	
	if oldState != state {
		cm.logger.Info("🔄 [%s] 连接状态变更: %s -> %s", cm.exchangeName, oldState.String(), state.String())
	}
}

// GetState 获取连接状态
func (cm *ConnectionManager) GetState() ConnectionState {
	cm.stateMutex.RLock()
	defer cm.stateMutex.RUnlock()
	return cm.state
}

// UpdateConnectionQuality 更新连接质量
func (cm *ConnectionManager) UpdateConnectionQuality(quality float64) {
	cm.qualityMutex.Lock()
	defer cm.qualityMutex.Unlock()
	
	// 使用移动平均来平滑质量变化
	cm.connectionQuality = cm.connectionQuality*0.8 + quality*0.2
	
	if cm.connectionQuality < 0.3 {
		cm.logger.Warn("⚠️ [%s] 连接质量较差: %.2f", cm.exchangeName, cm.connectionQuality)
	}
}

// getConnectionQuality 获取连接质量
func (cm *ConnectionManager) getConnectionQuality() float64 {
	cm.qualityMutex.RLock()
	defer cm.qualityMutex.RUnlock()
	return cm.connectionQuality
}

// isPeakHour 判断是否是高峰期（简单实现，可以根据实际情况调整）
func (cm *ConnectionManager) isPeakHour() bool {
	now := time.Now()
	hour := now.Hour()
	
	// 假设高峰期是 UTC 8:00-10:00, 14:00-16:00, 20:00-22:00
	return (hour >= 8 && hour < 10) || (hour >= 14 && hour < 16) || (hour >= 20 && hour < 22)
}

// startHealthCheck 启动健康检查
func (cm *ConnectionManager) startHealthCheck() {
	if cm.stabilityConfig == nil || cm.stabilityConfig.ConnectionHealthCheckInterval <= 0 {
		return
	}
	
	cm.healthCheckTicker = time.NewTicker(cm.stabilityConfig.ConnectionHealthCheckInterval)
	
	go func() {
		for {
			select {
			case <-cm.healthCheckTicker.C:
				cm.performHealthCheck()
			case <-cm.healthCheckStop:
				return
			}
		}
	}()
}

// performHealthCheck 执行健康检查
func (cm *ConnectionManager) performHealthCheck() {
	state := cm.GetState()
	quality := cm.getConnectionQuality()
	errorCount := atomic.LoadInt64(&cm.errorCount)
	successCount := atomic.LoadInt64(&cm.successCount)
	
	// 计算成功率
	totalAttempts := errorCount + successCount
	successRate := float64(successCount) / float64(totalAttempts)
	
	cm.logger.Debug("🔍 [%s] 健康检查 - 状态: %s, 质量: %.2f, 成功率: %.2f", 
		cm.exchangeName, state.String(), quality, successRate)
	
	// 根据健康状况调整策略
	if quality < 0.3 || successRate < 0.7 {
		cm.logger.Warn("⚠️ [%s] 连接健康状况不佳，建议检查网络或调整配置", cm.exchangeName)
	}
}

// Stop 停止连接管理器
func (cm *ConnectionManager) Stop() {
	cm.SetState(Stopped)
	
	if cm.healthCheckTicker != nil {
		cm.healthCheckTicker.Stop()
	}
	
	close(cm.healthCheckStop)
}

// GetStats 获取统计信息
func (cm *ConnectionManager) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"exchange":           cm.exchangeName,
		"state":              cm.GetState().String(),
		"connection_quality": cm.getConnectionQuality(),
		"error_count":        atomic.LoadInt64(&cm.errorCount),
		"success_count":      atomic.LoadInt64(&cm.successCount),
		"last_connect_time":  cm.lastConnectTime,
		"last_error_time":    cm.lastErrorTime,
		"strategy":           string(cm.strategy),
	}
}
