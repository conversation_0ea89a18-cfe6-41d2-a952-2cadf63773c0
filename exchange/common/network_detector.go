package common

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// NetworkDetector 网络检测器
type NetworkDetector struct {
	logger           *logrus.Logger
	exchangeEndpoints map[string][]string
	healthStatus     map[string]bool
	statusMutex      sync.RWMutex
	lastCheck        time.Time
	checkInterval    time.Duration
}

// NewNetworkDetector 创建网络检测器
func NewNetworkDetector(logger *logrus.Logger) *NetworkDetector {
	return &NetworkDetector{
		logger: logger,
		exchangeEndpoints: map[string][]string{
			"binance": {
				"https://fapi.binance.com/fapi/v1/ping",
				"https://api.binance.com/api/v3/ping",
			},
			"okx": {
				"https://www.okx.com/api/v5/public/time",
				"https://aws.okx.com/api/v5/public/time",
			},
			"bybit": {
				"https://api.bybit.com/v5/market/time",
				"https://api-testnet.bybit.com/v5/market/time",
			},
			"gateio": {
				"https://api.gateio.ws/api/v4/spot/time",
				"https://fx-api.gateio.ws/api/v4/futures/usdt/time",
			},
			"bitget": {
				"https://api.bitget.com/api/spot/v1/public/time",
				"https://api.bitget.com/api/mix/v1/market/contracts?productType=UMCBL",
			},
		},
		healthStatus:  make(map[string]bool),
		checkInterval: 30 * time.Second,
	}
}

// CheckExchangeConnectivity 检查交易所连通性
func (nd *NetworkDetector) CheckExchangeConnectivity(exchangeName string) bool {
	nd.statusMutex.RLock()
	if time.Since(nd.lastCheck) < nd.checkInterval {
		status, exists := nd.healthStatus[exchangeName]
		nd.statusMutex.RUnlock()
		if exists {
			return status
		}
	}
	nd.statusMutex.RUnlock()

	// 执行检查
	return nd.performConnectivityCheck(exchangeName)
}

// performConnectivityCheck 执行连通性检查
func (nd *NetworkDetector) performConnectivityCheck(exchangeName string) bool {
	endpoints, exists := nd.exchangeEndpoints[exchangeName]
	if !exists {
		nd.logger.Warnf("⚠️ 未知交易所: %s", exchangeName)
		return false
	}

	nd.logger.Debugf("🔍 检查 %s 连通性...", exchangeName)

	// 并行检查所有端点
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	results := make(chan bool, len(endpoints))
	
	for _, endpoint := range endpoints {
		go func(url string) {
			results <- nd.checkSingleEndpoint(ctx, url)
		}(endpoint)
	}

	// 等待结果
	successCount := 0
	for i := 0; i < len(endpoints); i++ {
		if <-results {
			successCount++
		}
	}

	// 至少一个端点成功就认为连通
	isHealthy := successCount > 0
	
	nd.statusMutex.Lock()
	nd.healthStatus[exchangeName] = isHealthy
	nd.lastCheck = time.Now()
	nd.statusMutex.Unlock()

	if isHealthy {
		nd.logger.Infof("✅ %s 网络连通性正常 (%d/%d 端点可用)", 
			exchangeName, successCount, len(endpoints))
	} else {
		nd.logger.Errorf("❌ %s 网络连通性异常 (0/%d 端点可用)", 
			exchangeName, len(endpoints))
	}

	return isHealthy
}

// checkSingleEndpoint 检查单个端点
func (nd *NetworkDetector) checkSingleEndpoint(ctx context.Context, url string) bool {
	// 首先检查 DNS 解析
	if !nd.checkDNSResolution(url) {
		return false
	}

	// 然后检查 HTTP 连接
	client := &http.Client{
		Timeout: 5 * time.Second,
		Transport: &http.Transport{
			DialContext: (&net.Dialer{
				Timeout: 3 * time.Second,
			}).DialContext,
		},
	}

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return false
	}

	resp, err := client.Do(req)
	if err != nil {
		nd.logger.Debugf("🔍 端点检查失败 %s: %v", url, err)
		return false
	}
	defer resp.Body.Close()

	// 2xx 或 4xx 都认为是连通的（4xx 表示服务器可达但请求有问题）
	return resp.StatusCode < 500
}

// checkDNSResolution 检查 DNS 解析
func (nd *NetworkDetector) checkDNSResolution(url string) bool {
	// 提取主机名
	if strings.HasPrefix(url, "http://") {
		url = url[7:]
	} else if strings.HasPrefix(url, "https://") {
		url = url[8:]
	}
	
	if idx := strings.Index(url, "/"); idx != -1 {
		url = url[:idx]
	}
	
	if idx := strings.Index(url, ":"); idx != -1 {
		url = url[:idx]
	}

	// 尝试 DNS 解析
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	_, err := net.DefaultResolver.LookupHost(ctx, url)
	if err != nil {
		nd.logger.Debugf("🔍 DNS 解析失败 %s: %v", url, err)
		return false
	}

	return true
}

// GetNetworkDiagnostics 获取网络诊断信息
func (nd *NetworkDetector) GetNetworkDiagnostics() map[string]interface{} {
	nd.statusMutex.RLock()
	defer nd.statusMutex.RUnlock()

	diagnostics := map[string]interface{}{
		"last_check": nd.lastCheck,
		"exchanges":  make(map[string]interface{}),
	}

	for exchange, status := range nd.healthStatus {
		diagnostics["exchanges"].(map[string]interface{})[exchange] = map[string]interface{}{
			"healthy":   status,
			"endpoints": len(nd.exchangeEndpoints[exchange]),
		}
	}

	return diagnostics
}

// SuggestAlternativeEndpoint 建议替代端点
func (nd *NetworkDetector) SuggestAlternativeEndpoint(exchangeName string) string {
	endpoints, exists := nd.exchangeEndpoints[exchangeName]
	if !exists || len(endpoints) < 2 {
		return ""
	}

	// 测试所有端点，返回第一个可用的
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	for _, endpoint := range endpoints {
		if nd.checkSingleEndpoint(ctx, endpoint) {
			return endpoint
		}
	}

	return ""
}

// ErrorAnalysis 错误分析
type ErrorAnalysis struct {
	ErrorType   string `json:"error_type"`
	Severity    string `json:"severity"`
	Suggestion  string `json:"suggestion"`
	Recoverable bool   `json:"recoverable"`
}

// AnalyzeNetworkError 分析网络错误
func (nd *NetworkDetector) AnalyzeNetworkError(err error, exchangeName string) *ErrorAnalysis {
	if err == nil {
		return &ErrorAnalysis{
			ErrorType:   "no_error",
			Severity:    "info",
			Suggestion:  "正常",
			Recoverable: true,
		}
	}

	errStr := strings.ToLower(err.Error())

	switch {
	case strings.Contains(errStr, "no such host"):
		return &ErrorAnalysis{
			ErrorType:   "dns_resolution_failed",
			Severity:    "high",
			Suggestion:  fmt.Sprintf("DNS 解析失败，建议检查网络连接或使用备用端点"),
			Recoverable: true,
		}

	case strings.Contains(errStr, "connection refused"):
		return &ErrorAnalysis{
			ErrorType:   "connection_refused",
			Severity:    "high",
			Suggestion:  "连接被拒绝，可能是防火墙或服务器问题",
			Recoverable: true,
		}

	case strings.Contains(errStr, "timeout"):
		return &ErrorAnalysis{
			ErrorType:   "timeout",
			Severity:    "medium",
			Suggestion:  "连接超时，建议增加超时时间或检查网络延迟",
			Recoverable: true,
		}

	case strings.Contains(errStr, "eof"):
		return &ErrorAnalysis{
			ErrorType:   "connection_closed",
			Severity:    "medium",
			Suggestion:  "连接被意外关闭，建议减少并发连接数",
			Recoverable: true,
		}

	case strings.Contains(errStr, "too many"):
		return &ErrorAnalysis{
			ErrorType:   "rate_limited",
			Severity:    "medium",
			Suggestion:  "请求过于频繁，建议增加延迟或减少并发",
			Recoverable: true,
		}

	default:
		return &ErrorAnalysis{
			ErrorType:   "unknown",
			Severity:    "low",
			Suggestion:  "未知错误，建议查看详细日志",
			Recoverable: true,
		}
	}
}

// StartPeriodicCheck 启动定期检查
func (nd *NetworkDetector) StartPeriodicCheck(ctx context.Context) {
	ticker := time.NewTicker(nd.checkInterval)
	defer ticker.Stop()

	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				nd.performFullCheck()
			}
		}
	}()
}

// performFullCheck 执行全面检查
func (nd *NetworkDetector) performFullCheck() {
	nd.logger.Debug("🔍 执行定期网络连通性检查...")
	
	for exchangeName := range nd.exchangeEndpoints {
		nd.performConnectivityCheck(exchangeName)
	}
}
