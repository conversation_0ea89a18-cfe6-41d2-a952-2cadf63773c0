package common

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// StartupStrategy 启动策略
type StartupStrategy string

const (
	SequentialStartup StartupStrategy = "sequential"  // 顺序启动
	ParallelStartup   StartupStrategy = "parallel"    // 并行启动
	StaggeredStartup  StartupStrategy = "staggered"   // 错峰启动
	AdaptiveStartup   StartupStrategy = "adaptive"    // 自适应启动
)

// ExchangeStartupConfig 交易所启动配置
type ExchangeStartupConfig struct {
	Name            string
	Priority        int           // 优先级，数字越小优先级越高
	StartupDelay    time.Duration // 启动延迟
	MaxRetries      int           // 最大重试次数
	RetryInterval   time.Duration // 重试间隔
	HealthCheck     bool          // 是否进行健康检查
	DependsOn       []string      // 依赖的交易所
}

// StartupManager 启动管理器
type StartupManager struct {
	logger           *logrus.Logger
	strategy         StartupStrategy
	networkDetector  *NetworkDetector
	exchangeConfigs  map[string]*ExchangeStartupConfig
	startupResults   map[string]error
	resultsMutex     sync.RWMutex
	
	// 统计信息
	totalExchanges   int
	successCount     int
	failureCount     int
	startTime        time.Time
	endTime          time.Time
}

// NewStartupManager 创建启动管理器
func NewStartupManager(logger *logrus.Logger, strategy StartupStrategy) *StartupManager {
	return &StartupManager{
		logger:          logger,
		strategy:        strategy,
		networkDetector: NewNetworkDetector(logger),
		exchangeConfigs: make(map[string]*ExchangeStartupConfig),
		startupResults:  make(map[string]error),
	}
}

// AddExchange 添加交易所配置
func (sm *StartupManager) AddExchange(config *ExchangeStartupConfig) {
	sm.exchangeConfigs[config.Name] = config
	sm.totalExchanges++
}

// StartExchanges 启动所有交易所
func (sm *StartupManager) StartExchanges(ctx context.Context, startFuncs map[string]func() error) error {
	sm.startTime = time.Now()
	sm.logger.Infof("🚀 开始启动 %d 个交易所，策略: %s", sm.totalExchanges, sm.strategy)

	// 启动网络检测
	sm.networkDetector.StartPeriodicCheck(ctx)

	var err error
	switch sm.strategy {
	case SequentialStartup:
		err = sm.startSequential(ctx, startFuncs)
	case ParallelStartup:
		err = sm.startParallel(ctx, startFuncs)
	case StaggeredStartup:
		err = sm.startStaggered(ctx, startFuncs)
	case AdaptiveStartup:
		err = sm.startAdaptive(ctx, startFuncs)
	default:
		err = sm.startSequential(ctx, startFuncs)
	}

	sm.endTime = time.Now()
	sm.logStartupSummary()
	
	return err
}

// startSequential 顺序启动
func (sm *StartupManager) startSequential(ctx context.Context, startFuncs map[string]func() error) error {
	// 按优先级排序
	sortedExchanges := sm.getSortedExchanges()
	
	for _, exchangeName := range sortedExchanges {
		config := sm.exchangeConfigs[exchangeName]
		startFunc := startFuncs[exchangeName]
		
		if startFunc == nil {
			sm.recordResult(exchangeName, fmt.Errorf("启动函数未找到"))
			continue
		}

		sm.logger.Infof("🔄 启动交易所: %s", exchangeName)
		
		// 检查依赖
		if !sm.checkDependencies(exchangeName) {
			sm.recordResult(exchangeName, fmt.Errorf("依赖检查失败"))
			continue
		}

		// 启动延迟
		if config.StartupDelay > 0 {
			sm.logger.Debugf("⏳ %s 启动延迟 %v", exchangeName, config.StartupDelay)
			time.Sleep(config.StartupDelay)
		}

		// 尝试启动
		err := sm.startWithRetry(ctx, exchangeName, startFunc)
		sm.recordResult(exchangeName, err)
		
		if err != nil {
			sm.logger.Errorf("❌ 交易所 %s 启动失败: %v", exchangeName, err)
		} else {
			sm.logger.Infof("✅ 交易所 %s 启动成功", exchangeName)
		}
	}
	
	return nil
}

// startParallel 并行启动
func (sm *StartupManager) startParallel(ctx context.Context, startFuncs map[string]func() error) error {
	var wg sync.WaitGroup
	
	for exchangeName, startFunc := range startFuncs {
		if startFunc == nil {
			sm.recordResult(exchangeName, fmt.Errorf("启动函数未找到"))
			continue
		}

		wg.Add(1)
		go func(name string, fn func() error) {
			defer wg.Done()
			
			config := sm.exchangeConfigs[name]
			sm.logger.Infof("🔄 启动交易所: %s", name)
			
			// 启动延迟
			if config != nil && config.StartupDelay > 0 {
				time.Sleep(config.StartupDelay)
			}

			err := sm.startWithRetry(ctx, name, fn)
			sm.recordResult(name, err)
			
			if err != nil {
				sm.logger.Errorf("❌ 交易所 %s 启动失败: %v", name, err)
			} else {
				sm.logger.Infof("✅ 交易所 %s 启动成功", name)
			}
		}(exchangeName, startFunc)
	}
	
	wg.Wait()
	return nil
}

// startStaggered 错峰启动
func (sm *StartupManager) startStaggered(ctx context.Context, startFuncs map[string]func() error) error {
	sortedExchanges := sm.getSortedExchanges()
	staggerDelay := 5 * time.Second // 错峰间隔
	
	var wg sync.WaitGroup
	
	for i, exchangeName := range sortedExchanges {
		startFunc := startFuncs[exchangeName]
		if startFunc == nil {
			sm.recordResult(exchangeName, fmt.Errorf("启动函数未找到"))
			continue
		}

		wg.Add(1)
		go func(name string, fn func() error, delay time.Duration) {
			defer wg.Done()
			
			// 错峰延迟
			time.Sleep(delay)
			
			sm.logger.Infof("🔄 启动交易所: %s", name)
			err := sm.startWithRetry(ctx, name, fn)
			sm.recordResult(name, err)
			
			if err != nil {
				sm.logger.Errorf("❌ 交易所 %s 启动失败: %v", name, err)
			} else {
				sm.logger.Infof("✅ 交易所 %s 启动成功", name)
			}
		}(exchangeName, startFunc, time.Duration(i)*staggerDelay)
	}
	
	wg.Wait()
	return nil
}

// startAdaptive 自适应启动
func (sm *StartupManager) startAdaptive(ctx context.Context, startFuncs map[string]func() error) error {
	// 首先检查所有交易所的网络状况
	healthyExchanges := make([]string, 0)
	unhealthyExchanges := make([]string, 0)
	
	for exchangeName := range startFuncs {
		if sm.networkDetector.CheckExchangeConnectivity(exchangeName) {
			healthyExchanges = append(healthyExchanges, exchangeName)
		} else {
			unhealthyExchanges = append(unhealthyExchanges, exchangeName)
		}
	}
	
	sm.logger.Infof("📊 网络状况检查: %d 个交易所健康, %d 个交易所异常", 
		len(healthyExchanges), len(unhealthyExchanges))
	
	// 先启动健康的交易所（并行）
	if len(healthyExchanges) > 0 {
		sm.logger.Info("🚀 优先启动网络状况良好的交易所...")
		healthyStartFuncs := make(map[string]func() error)
		for _, name := range healthyExchanges {
			healthyStartFuncs[name] = startFuncs[name]
		}
		sm.startParallel(ctx, healthyStartFuncs)
	}
	
	// 再启动异常的交易所（顺序，增加重试）
	if len(unhealthyExchanges) > 0 {
		sm.logger.Info("🔄 启动网络状况异常的交易所（增强重试）...")
		for _, exchangeName := range unhealthyExchanges {
			startFunc := startFuncs[exchangeName]
			if startFunc == nil {
				sm.recordResult(exchangeName, fmt.Errorf("启动函数未找到"))
				continue
			}
			
			// 增加重试次数和延迟
			config := sm.exchangeConfigs[exchangeName]
			if config != nil {
				config.MaxRetries = config.MaxRetries * 2
				config.RetryInterval = config.RetryInterval * 2
			}
			
			sm.logger.Infof("🔄 启动交易所: %s (网络异常，增强重试)", exchangeName)
			err := sm.startWithRetry(ctx, exchangeName, startFunc)
			sm.recordResult(exchangeName, err)
			
			if err != nil {
				sm.logger.Errorf("❌ 交易所 %s 启动失败: %v", exchangeName, err)
			} else {
				sm.logger.Infof("✅ 交易所 %s 启动成功", exchangeName)
			}
			
			// 异常交易所之间增加更长的间隔
			time.Sleep(10 * time.Second)
		}
	}
	
	return nil
}

// startWithRetry 带重试的启动
func (sm *StartupManager) startWithRetry(ctx context.Context, exchangeName string, startFunc func() error) error {
	config := sm.exchangeConfigs[exchangeName]
	maxRetries := 3
	retryInterval := 5 * time.Second
	
	if config != nil {
		maxRetries = config.MaxRetries
		retryInterval = config.RetryInterval
	}
	
	var lastErr error
	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			sm.logger.Warnf("🔄 重试启动 %s (第%d次)", exchangeName, attempt)
			time.Sleep(retryInterval)
		}
		
		err := startFunc()
		if err == nil {
			return nil
		}
		
		lastErr = err
		sm.logger.Warnf("⚠️ %s 启动失败 (第%d次): %v", exchangeName, attempt+1, err)
	}
	
	return fmt.Errorf("重试 %d 次后仍然失败: %v", maxRetries, lastErr)
}

// getSortedExchanges 获取按优先级排序的交易所列表
func (sm *StartupManager) getSortedExchanges() []string {
	type exchangePriority struct {
		name     string
		priority int
	}
	
	exchanges := make([]exchangePriority, 0, len(sm.exchangeConfigs))
	for name, config := range sm.exchangeConfigs {
		exchanges = append(exchanges, exchangePriority{name, config.Priority})
	}
	
	// 简单的冒泡排序
	for i := 0; i < len(exchanges); i++ {
		for j := i + 1; j < len(exchanges); j++ {
			if exchanges[i].priority > exchanges[j].priority {
				exchanges[i], exchanges[j] = exchanges[j], exchanges[i]
			}
		}
	}
	
	result := make([]string, len(exchanges))
	for i, ex := range exchanges {
		result[i] = ex.name
	}
	
	return result
}

// checkDependencies 检查依赖
func (sm *StartupManager) checkDependencies(exchangeName string) bool {
	config := sm.exchangeConfigs[exchangeName]
	if config == nil || len(config.DependsOn) == 0 {
		return true
	}
	
	sm.resultsMutex.RLock()
	defer sm.resultsMutex.RUnlock()
	
	for _, dep := range config.DependsOn {
		if err, exists := sm.startupResults[dep]; !exists || err != nil {
			return false
		}
	}
	
	return true
}

// recordResult 记录结果
func (sm *StartupManager) recordResult(exchangeName string, err error) {
	sm.resultsMutex.Lock()
	defer sm.resultsMutex.Unlock()
	
	sm.startupResults[exchangeName] = err
	if err == nil {
		sm.successCount++
	} else {
		sm.failureCount++
	}
}

// logStartupSummary 记录启动总结
func (sm *StartupManager) logStartupSummary() {
	duration := sm.endTime.Sub(sm.startTime)
	
	sm.logger.Infof("📊 启动完成总结:")
	sm.logger.Infof("   总耗时: %v", duration)
	sm.logger.Infof("   成功: %d/%d", sm.successCount, sm.totalExchanges)
	sm.logger.Infof("   失败: %d/%d", sm.failureCount, sm.totalExchanges)
	
	if sm.failureCount > 0 {
		sm.logger.Warn("❌ 失败的交易所:")
		sm.resultsMutex.RLock()
		for name, err := range sm.startupResults {
			if err != nil {
				sm.logger.Warnf("   %s: %v", name, err)
			}
		}
		sm.resultsMutex.RUnlock()
	}
}

// GetStartupResults 获取启动结果
func (sm *StartupManager) GetStartupResults() map[string]error {
	sm.resultsMutex.RLock()
	defer sm.resultsMutex.RUnlock()
	
	results := make(map[string]error)
	for name, err := range sm.startupResults {
		results[name] = err
	}
	
	return results
}
