package common

import (
	"context"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ConnectionPool 连接池管理器
type ConnectionPool struct {
	exchangeName     string
	maxConnections   int
	currentConnections int
	connectionMutex  sync.RWMutex
	logger           *logrus.Logger
	
	// 启动控制
	startupDelay     time.Duration
	batchStartDelay  time.Duration
	
	// 连接质量监控
	successCount     int64
	failureCount     int64
	qualityMutex     sync.RWMutex
	
	// 动态调整
	originalBatchSize int
	currentBatchSize  int
	adjustmentMutex   sync.RWMutex
}

// NewConnectionPool 创建连接池
func NewConnectionPool(exchangeName string, maxConnections int, originalBatchSize int, logger *logrus.Logger) *ConnectionPool {
	return &ConnectionPool{
		exchangeName:      exchangeName,
		maxConnections:    maxConnections,
		currentConnections: 0,
		logger:            logger,
		startupDelay:      2 * time.Second,
		batchStartDelay:   500 * time.Millisecond,
		originalBatchSize: originalBatchSize,
		currentBatchSize:  originalBatchSize,
	}
}

// CanCreateConnection 检查是否可以创建新连接
func (cp *ConnectionPool) CanCreateConnection() bool {
	cp.connectionMutex.RLock()
	defer cp.connectionMutex.RUnlock()
	
	return cp.currentConnections < cp.maxConnections
}

// AcquireConnection 获取连接许可
func (cp *ConnectionPool) AcquireConnection() bool {
	cp.connectionMutex.Lock()
	defer cp.connectionMutex.Unlock()
	
	if cp.currentConnections >= cp.maxConnections {
		return false
	}
	
	cp.currentConnections++
	cp.logger.Debugf("🔗 [%s] 获取连接许可，当前连接数: %d/%d", 
		cp.exchangeName, cp.currentConnections, cp.maxConnections)
	return true
}

// ReleaseConnection 释放连接许可
func (cp *ConnectionPool) ReleaseConnection() {
	cp.connectionMutex.Lock()
	defer cp.connectionMutex.Unlock()
	
	if cp.currentConnections > 0 {
		cp.currentConnections--
		cp.logger.Debugf("🔓 [%s] 释放连接许可，当前连接数: %d/%d", 
			cp.exchangeName, cp.currentConnections, cp.maxConnections)
	}
}

// WaitForConnectionSlot 等待连接槽位
func (cp *ConnectionPool) WaitForConnectionSlot(ctx context.Context, timeout time.Duration) bool {
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()
	
	timeoutTimer := time.NewTimer(timeout)
	defer timeoutTimer.Stop()
	
	for {
		if cp.AcquireConnection() {
			return true
		}
		
		select {
		case <-ctx.Done():
			return false
		case <-timeoutTimer.C:
			cp.logger.Warnf("⏰ [%s] 等待连接槽位超时", cp.exchangeName)
			return false
		case <-ticker.C:
			// 继续等待
		}
	}
}

// RecordConnectionResult 记录连接结果
func (cp *ConnectionPool) RecordConnectionResult(success bool) {
	cp.qualityMutex.Lock()
	defer cp.qualityMutex.Unlock()
	
	if success {
		cp.successCount++
	} else {
		cp.failureCount++
	}
	
	// 动态调整批次大小
	cp.adjustBatchSize()
}

// adjustBatchSize 动态调整批次大小
func (cp *ConnectionPool) adjustBatchSize() {
	totalAttempts := cp.successCount + cp.failureCount
	if totalAttempts < 10 {
		return // 样本太少，不调整
	}
	
	successRate := float64(cp.successCount) / float64(totalAttempts)
	
	cp.adjustmentMutex.Lock()
	defer cp.adjustmentMutex.Unlock()
	
	oldBatchSize := cp.currentBatchSize
	
	if successRate < 0.5 {
		// 成功率低，减小批次
		cp.currentBatchSize = int(float64(cp.currentBatchSize) * 0.7)
		if cp.currentBatchSize < 5 {
			cp.currentBatchSize = 5
		}
	} else if successRate > 0.8 && cp.currentBatchSize < cp.originalBatchSize {
		// 成功率高，适当增加批次
		cp.currentBatchSize = int(float64(cp.currentBatchSize) * 1.2)
		if cp.currentBatchSize > cp.originalBatchSize {
			cp.currentBatchSize = cp.originalBatchSize
		}
	}
	
	if oldBatchSize != cp.currentBatchSize {
		cp.logger.Infof("📊 [%s] 动态调整批次大小: %d -> %d (成功率: %.2f)", 
			cp.exchangeName, oldBatchSize, cp.currentBatchSize, successRate)
	}
}

// GetCurrentBatchSize 获取当前批次大小
func (cp *ConnectionPool) GetCurrentBatchSize() int {
	cp.adjustmentMutex.RLock()
	defer cp.adjustmentMutex.RUnlock()
	return cp.currentBatchSize
}

// GetConnectionStats 获取连接统计
func (cp *ConnectionPool) GetConnectionStats() map[string]interface{} {
	cp.connectionMutex.RLock()
	cp.qualityMutex.RLock()
	cp.adjustmentMutex.RLock()
	defer cp.connectionMutex.RUnlock()
	defer cp.qualityMutex.RUnlock()
	defer cp.adjustmentMutex.RUnlock()
	
	totalAttempts := cp.successCount + cp.failureCount
	successRate := float64(0)
	if totalAttempts > 0 {
		successRate = float64(cp.successCount) / float64(totalAttempts)
	}
	
	return map[string]interface{}{
		"exchange":            cp.exchangeName,
		"current_connections": cp.currentConnections,
		"max_connections":     cp.maxConnections,
		"success_count":       cp.successCount,
		"failure_count":       cp.failureCount,
		"success_rate":        successRate,
		"original_batch_size": cp.originalBatchSize,
		"current_batch_size":  cp.currentBatchSize,
	}
}

// StartupDelay 启动延迟
func (cp *ConnectionPool) StartupDelay() {
	if cp.startupDelay > 0 {
		cp.logger.Infof("⏳ [%s] 启动延迟 %v", cp.exchangeName, cp.startupDelay)
		time.Sleep(cp.startupDelay)
	}
}

// BatchStartDelay 批次启动延迟
func (cp *ConnectionPool) BatchStartDelay() {
	if cp.batchStartDelay > 0 {
		time.Sleep(cp.batchStartDelay)
	}
}

// SetStartupDelay 设置启动延迟
func (cp *ConnectionPool) SetStartupDelay(delay time.Duration) {
	cp.startupDelay = delay
}

// SetBatchStartDelay 设置批次启动延迟
func (cp *ConnectionPool) SetBatchStartDelay(delay time.Duration) {
	cp.batchStartDelay = delay
}

// ErrorAnalyzer 错误分析器
type ErrorAnalyzer struct {
	exchangeName string
	logger       *logrus.Logger
	
	// 错误统计
	eofErrors        int64
	timeoutErrors    int64
	refusedErrors    int64
	otherErrors      int64
	errorMutex       sync.RWMutex
}

// NewErrorAnalyzer 创建错误分析器
func NewErrorAnalyzer(exchangeName string, logger *logrus.Logger) *ErrorAnalyzer {
	return &ErrorAnalyzer{
		exchangeName: exchangeName,
		logger:       logger,
	}
}

// AnalyzeError 分析错误类型
func (ea *ErrorAnalyzer) AnalyzeError(err error) string {
	if err == nil {
		return "no_error"
	}
	
	errStr := err.Error()
	ea.errorMutex.Lock()
	defer ea.errorMutex.Unlock()
	
	switch {
	case errStr == "EOF":
		ea.eofErrors++
		return "eof_error"
	case contains(errStr, "timeout"):
		ea.timeoutErrors++
		return "timeout_error"
	case contains(errStr, "refused") || contains(errStr, "reset"):
		ea.refusedErrors++
		return "connection_refused"
	default:
		ea.otherErrors++
		return "other_error"
	}
}

// GetRecommendedAction 获取推荐操作
func (ea *ErrorAnalyzer) GetRecommendedAction(errorType string) string {
	ea.errorMutex.RLock()
	defer ea.errorMutex.RUnlock()
	
	switch errorType {
	case "eof_error":
		if ea.eofErrors > 10 {
			return "reduce_batch_size_and_add_delay"
		}
		return "add_delay"
	case "timeout_error":
		return "increase_timeout"
	case "connection_refused":
		return "reduce_connection_frequency"
	default:
		return "standard_retry"
	}
}

// GetErrorStats 获取错误统计
func (ea *ErrorAnalyzer) GetErrorStats() map[string]interface{} {
	ea.errorMutex.RLock()
	defer ea.errorMutex.RUnlock()
	
	return map[string]interface{}{
		"exchange":       ea.exchangeName,
		"eof_errors":     ea.eofErrors,
		"timeout_errors": ea.timeoutErrors,
		"refused_errors": ea.refusedErrors,
		"other_errors":   ea.otherErrors,
	}
}

// contains 检查字符串是否包含子串（不区分大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    (len(s) > len(substr) && 
		     (s[:len(substr)] == substr || 
		      s[len(s)-len(substr):] == substr ||
		      findInString(s, substr))))
}

// findInString 在字符串中查找子串
func findInString(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
