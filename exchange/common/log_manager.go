package common

import (
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// LogLevel 日志级别
type LogLevel int

const (
	LogLevelDebug LogLevel = iota
	LogLevelInfo
	LogLevelWarn
	LogLevelError
)

// LogManager 日志管理器
type LogManager struct {
	logger       *logrus.Logger
	exchangeName string

	// 日志去重
	lastMessages map[string]time.Time
	messageMutex sync.RWMutex
	dedupeWindow time.Duration

	// 统计信息
	messageStats map[string]int64
	statsMutex   sync.RWMutex

	// 批量日志
	batchMessages []string
	batchMutex    sync.Mutex
	batchSize     int
	batchTimeout  time.Duration
	lastBatchTime time.Time
}

// NewLogManager 创建日志管理器
func NewLogManager(exchangeName string, logger *logrus.Logger) *LogManager {
	return &LogManager{
		logger:        logger,
		exchangeName:  exchangeName,
		lastMessages:  make(map[string]time.Time),
		messageStats:  make(map[string]int64),
		dedupeWindow:  30 * time.Second, // 30秒内相同消息去重
		batchSize:     10,
		batchTimeout:  5 * time.Second,
		lastBatchTime: time.Now(),
	}
}

// LogWithDedup 带去重的日志记录
func (lm *LogManager) LogWithDedup(level LogLevel, messageKey string, format string, args ...interface{}) {
	lm.messageMutex.Lock()
	defer lm.messageMutex.Unlock()

	now := time.Now()

	// 检查是否需要去重
	if lastTime, exists := lm.lastMessages[messageKey]; exists {
		if now.Sub(lastTime) < lm.dedupeWindow {
			// 在去重窗口内，只更新统计
			lm.updateStats(messageKey)
			return
		}
	}

	// 记录消息时间
	lm.lastMessages[messageKey] = now
	lm.updateStats(messageKey)

	// 输出日志
	switch level {
	case LogLevelDebug:
		lm.logger.Debugf(format, args...)
	case LogLevelInfo:
		lm.logger.Infof(format, args...)
	case LogLevelWarn:
		lm.logger.Warnf(format, args...)
	case LogLevelError:
		lm.logger.Errorf(format, args...)
	}
}

// LogSubscriptionSuccess 记录订阅成功（去重）
func (lm *LogManager) LogSubscriptionSuccess(dataType string) {
	messageKey := fmt.Sprintf("subscription_success_%s", dataType)
	lm.LogWithDedup(LogLevelInfo, messageKey, "✅ %s %s数据订阅确认成功", lm.exchangeName, dataType)
}

// LogConnectionSuccess 记录连接成功（去重）
func (lm *LogManager) LogConnectionSuccess(connName string) {
	messageKey := fmt.Sprintf("connection_success_%s", connName)
	lm.LogWithDedup(LogLevelInfo, messageKey, "✅ %s WebSocket连接成功 %s", lm.exchangeName, connName)
}

// LogConnectionError 记录连接错误
func (lm *LogManager) LogConnectionError(connName string, err error, errorType string, suggestion string) {
	messageKey := fmt.Sprintf("connection_error_%s", connName)
	lm.LogWithDedup(LogLevelError, messageKey,
		"❌ %s WebSocket连接失败 %s: %v (错误类型: %s, 建议: %s)",
		lm.exchangeName, connName, err, errorType, suggestion)
}

// LogReconnectAttempt 记录重连尝试
func (lm *LogManager) LogReconnectAttempt(connName string, attempt int, delay time.Duration) {
	messageKey := fmt.Sprintf("reconnect_%s", connName)
	lm.LogWithDedup(LogLevelWarn, messageKey,
		"🔄 %s连接 %s 准备重连 (第%d次)，等待 %v", lm.exchangeName, connName, attempt, delay)
}

// LogBatch 批量日志记录
func (lm *LogManager) LogBatch(message string) {
	lm.batchMutex.Lock()
	defer lm.batchMutex.Unlock()

	lm.batchMessages = append(lm.batchMessages, message)

	// 检查是否需要输出批量日志
	if len(lm.batchMessages) >= lm.batchSize ||
		time.Since(lm.lastBatchTime) > lm.batchTimeout {
		lm.flushBatch()
	}
}

// flushBatch 输出批量日志
func (lm *LogManager) flushBatch() {
	if len(lm.batchMessages) == 0 {
		return
	}

	// 合并相似消息
	messageCounts := make(map[string]int)
	for _, msg := range lm.batchMessages {
		messageCounts[msg]++
	}

	// 输出合并后的消息
	for msg, count := range messageCounts {
		if count > 1 {
			lm.logger.Infof("%s (x%d)", msg, count)
		} else {
			lm.logger.Info(msg)
		}
	}

	// 清空批量消息
	lm.batchMessages = lm.batchMessages[:0]
	lm.lastBatchTime = time.Now()
}

// updateStats 更新统计信息
func (lm *LogManager) updateStats(messageKey string) {
	lm.statsMutex.Lock()
	defer lm.statsMutex.Unlock()
	lm.messageStats[messageKey]++
}

// GetStats 获取日志统计
func (lm *LogManager) GetStats() map[string]interface{} {
	lm.statsMutex.RLock()
	defer lm.statsMutex.RUnlock()

	stats := make(map[string]interface{})
	stats["exchange"] = lm.exchangeName
	stats["message_counts"] = make(map[string]int64)

	for key, count := range lm.messageStats {
		stats["message_counts"].(map[string]int64)[key] = count
	}

	return stats
}

// Cleanup 清理过期的日志记录
func (lm *LogManager) Cleanup() {
	lm.messageMutex.Lock()
	defer lm.messageMutex.Unlock()

	now := time.Now()
	for key, lastTime := range lm.lastMessages {
		if now.Sub(lastTime) > lm.dedupeWindow*2 {
			delete(lm.lastMessages, key)
		}
	}
}

// SetDedupeWindow 设置去重窗口
func (lm *LogManager) SetDedupeWindow(window time.Duration) {
	lm.dedupeWindow = window
}

// StartPeriodicCleanup 启动定期清理
func (lm *LogManager) StartPeriodicCleanup() {
	go func() {
		ticker := time.NewTicker(5 * time.Minute)
		defer ticker.Stop()

		for range ticker.C {
			lm.Cleanup()
		}
	}()
}

// ConnectionQualityTracker 连接质量跟踪器
type ConnectionQualityTracker struct {
	exchangeName string
	logger       *logrus.Logger

	// 质量指标
	successCount   int64
	failureCount   int64
	totalAttempts  int64
	lastUpdateTime time.Time
	qualityMutex   sync.RWMutex

	// 质量历史
	qualityHistory []float64
	historyMutex   sync.Mutex
	maxHistorySize int

	// 警告控制
	lastWarningTime time.Time
	warningInterval time.Duration
}

// NewConnectionQualityTracker 创建连接质量跟踪器
func NewConnectionQualityTracker(exchangeName string, logger *logrus.Logger) *ConnectionQualityTracker {
	return &ConnectionQualityTracker{
		exchangeName:    exchangeName,
		logger:          logger,
		maxHistorySize:  100,
		warningInterval: 60 * time.Second, // 1分钟内最多一次警告
		lastUpdateTime:  time.Now(),
	}
}

// RecordSuccess 记录成功
func (cqt *ConnectionQualityTracker) RecordSuccess() {
	cqt.qualityMutex.Lock()
	defer cqt.qualityMutex.Unlock()

	cqt.successCount++
	cqt.totalAttempts++
	cqt.lastUpdateTime = time.Now()

	cqt.updateQualityHistory()
}

// RecordFailure 记录失败
func (cqt *ConnectionQualityTracker) RecordFailure() {
	cqt.qualityMutex.Lock()
	defer cqt.qualityMutex.Unlock()

	cqt.failureCount++
	cqt.totalAttempts++
	cqt.lastUpdateTime = time.Now()

	cqt.updateQualityHistory()
	cqt.checkAndWarnIfNeeded()
}

// GetQuality 获取连接质量 (0.0 - 1.0)
func (cqt *ConnectionQualityTracker) GetQuality() float64 {
	cqt.qualityMutex.RLock()
	defer cqt.qualityMutex.RUnlock()

	if cqt.totalAttempts == 0 {
		return 1.0 // 初始状态认为是好的
	}

	return float64(cqt.successCount) / float64(cqt.totalAttempts)
}

// updateQualityHistory 更新质量历史
func (cqt *ConnectionQualityTracker) updateQualityHistory() {
	quality := float64(cqt.successCount) / float64(cqt.totalAttempts)

	cqt.historyMutex.Lock()
	defer cqt.historyMutex.Unlock()

	cqt.qualityHistory = append(cqt.qualityHistory, quality)

	// 限制历史大小
	if len(cqt.qualityHistory) > cqt.maxHistorySize {
		cqt.qualityHistory = cqt.qualityHistory[1:]
	}
}

// checkAndWarnIfNeeded 检查并在需要时发出警告
func (cqt *ConnectionQualityTracker) checkAndWarnIfNeeded() {
	now := time.Now()

	// 检查警告间隔
	if now.Sub(cqt.lastWarningTime) < cqt.warningInterval {
		return
	}

	quality := float64(cqt.successCount) / float64(cqt.totalAttempts)

	// 只有在质量确实很差且有足够样本时才警告
	if quality < 0.3 && cqt.totalAttempts >= 10 {
		cqt.logger.Warnf("⚠️ [%s] 连接质量较差: %.2f (成功: %d, 失败: %d)",
			cqt.exchangeName, quality, cqt.successCount, cqt.failureCount)
		cqt.lastWarningTime = now
	}
}

// GetStats 获取统计信息
func (cqt *ConnectionQualityTracker) GetStats() map[string]interface{} {
	cqt.qualityMutex.RLock()
	defer cqt.qualityMutex.RUnlock()

	return map[string]interface{}{
		"exchange":       cqt.exchangeName,
		"success_count":  cqt.successCount,
		"failure_count":  cqt.failureCount,
		"total_attempts": cqt.totalAttempts,
		"quality":        cqt.GetQuality(),
		"last_update":    cqt.lastUpdateTime,
	}
}

// Reset 重置统计
func (cqt *ConnectionQualityTracker) Reset() {
	cqt.qualityMutex.Lock()
	defer cqt.qualityMutex.Unlock()

	cqt.successCount = 0
	cqt.failureCount = 0
	cqt.totalAttempts = 0
	cqt.lastUpdateTime = time.Now()

	cqt.historyMutex.Lock()
	cqt.qualityHistory = cqt.qualityHistory[:0]
	cqt.historyMutex.Unlock()
}
