package bybit

import (
	"exchange_signal/exchange"
)

// BybitClient Bybit交易所客户端
type BybitClient struct {
	*exchange.BaseExchangeClient
}

// NewBybitClient 创建新的Bybit客户端
func NewBybitClient() *BybitClient {
	baseClient := exchange.NewBaseExchangeClient("bybit")

	return &BybitClient{
		BaseExchangeClient: baseClient,
	}
}

// Start 启动Bybit客户端（占位符实现）
func (bc *BybitClient) Start() error {
	// TODO: 实现Bybit具体逻辑
	return nil
}
