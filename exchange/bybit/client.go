package bybit

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/sirupsen/logrus"

	"exchange_signal/config"
	"exchange_signal/exchange"
	"exchange_signal/exchange/common"
)

// BybitClient Bybit交易所客户端
type BybitClient struct {
	*exchange.BaseExchangeClient

	// Bybit特定配置
	logger *logrus.Logger

	// WebSocket管理
	tradeManager     *BybitTradeDataManager
	orderbookManager *BybitOrderbookDataManager

	// 网络检测
	networkDetector *common.NetworkDetector

	// 连接控制
	wg sync.WaitGroup
}

// SymbolInfo Bybit交易对信息
type BybitSymbolInfo struct {
	Symbol       string `json:"symbol"`
	ContractType string `json:"contractType"`
	Status       string `json:"status"`
	BaseCoin     string `json:"baseCoin"`
	QuoteCoin    string `json:"quoteCoin"`
}

// InstrumentsResponse Bybit交易对响应
type BybitInstrumentsResponse struct {
	RetCode int    `json:"retCode"`
	RetMsg  string `json:"retMsg"`
	Result  struct {
		Category string            `json:"category"`
		List     []BybitSymbolInfo `json:"list"`
	} `json:"result"`
}

// NewBybitClient 创建新的Bybit客户端
func NewBybitClient() *BybitClient {
	baseClient := exchange.NewBaseExchangeClient("bybit")

	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})

	client := &BybitClient{
		BaseExchangeClient: baseClient,
		logger:             logger,
		networkDetector:    common.NewNetworkDetector(logger),
	}

	return client
}

// SetConfig 设置配置
func (bc *BybitClient) SetConfig(cfg *config.ExchangeConfig) {
	bc.BaseExchangeClient.SetConfig(cfg)

	// 设置日志级别
	if level, err := logrus.ParseLevel("info"); err == nil {
		bc.logger.SetLevel(level)
	}
}

// GetSymbols 获取所有永续合约交易对
func (bc *BybitClient) GetSymbols() ([]string, error) {
	bc.logger.Info("🔄 正在获取Bybit永续合约交易对...")

	if bc.Config == nil {
		return nil, fmt.Errorf("配置未设置")
	}

	// 检查网络连通性
	if !bc.networkDetector.CheckExchangeConnectivity("bybit") {
		// 尝试获取替代端点
		altEndpoint := bc.networkDetector.SuggestAlternativeEndpoint("bybit")
		if altEndpoint != "" {
			bc.logger.Warnf("⚠️ 主端点不可用，尝试使用替代端点: %s", altEndpoint)
		} else {
			return nil, fmt.Errorf("网络连通性检查失败，无可用端点")
		}
	}

	// 创建带超时的HTTP客户端
	client := &http.Client{
		Timeout: 15 * time.Second,
	}

	url := bc.Config.APIBaseURL + "/v5/market/instruments-info?category=linear"
	resp, err := client.Get(url)
	if err != nil {
		// 分析错误并提供建议
		analysis := bc.networkDetector.AnalyzeNetworkError(err, "bybit")
		bc.logger.Errorf("❌ 网络错误分析: %s - %s", analysis.ErrorType, analysis.Suggestion)

		return nil, fmt.Errorf("获取交易对信息失败: %v", err)
	}
	defer resp.Body.Close()

	var instrumentsResp BybitInstrumentsResponse
	if err := json.NewDecoder(resp.Body).Decode(&instrumentsResp); err != nil {
		return nil, fmt.Errorf("解析交易对信息失败: %v", err)
	}

	if instrumentsResp.RetCode != 0 {
		return nil, fmt.Errorf("Bybit API错误: %s", instrumentsResp.RetMsg)
	}

	symbols := make([]string, 0)
	for _, symbol := range instrumentsResp.Result.List {
		if symbol.Status == "Trading" {
			// 应用交易对过滤器
			if bc.shouldIncludeSymbol(symbol.Symbol) {
				symbols = append(symbols, symbol.Symbol)
			}
		}
	}

	bc.Symbols = symbols
	bc.logger.Infof("✅ 成功获取 %d 个Bybit交易对", len(symbols))
	return symbols, nil
}

// shouldIncludeSymbol 检查是否应包含该交易对
func (bc *BybitClient) shouldIncludeSymbol(symbol string) bool {
	if bc.Config == nil || len(bc.Config.SymbolFilter) == 0 {
		return true // 无过滤器，包含所有
	}

	for _, filter := range bc.Config.SymbolFilter {
		if symbol == filter {
			return true
		}
	}
	return false
}

// Connect 连接到Bybit
func (bc *BybitClient) Connect(ctx context.Context) error {
	bc.logger.Info("🔗 连接到Bybit...")

	if bc.Config == nil {
		return fmt.Errorf("配置未设置")
	}

	// 获取交易对
	symbols, err := bc.GetSymbols()
	if err != nil {
		return fmt.Errorf("获取交易对失败: %v", err)
	}

	if len(symbols) == 0 {
		return fmt.Errorf("没有可用的交易对")
	}

	// 初始化数据管理器
	bc.tradeManager = &BybitTradeDataManager{client: bc, symbols: symbols}
	bc.orderbookManager = &BybitOrderbookDataManager{client: bc, symbols: symbols}

	bc.Status.State = "connected"
	bc.Status.ConnectedTime = time.Now()

	bc.logger.Info("✅ Bybit连接成功")
	return nil
}

// Start 启动Bybit客户端
func (bc *BybitClient) Start() error {
	if err := bc.Connect(bc.Ctx); err != nil {
		return err
	}

	bc.Status.State = "connecting"
	bc.Stats.StartTime = time.Now()

	// 启动交易数据订阅
	if bc.Config.EnableTradeData {
		bc.wg.Add(1)
		go func() {
			defer bc.wg.Done()
			if err := bc.tradeManager.Start(); err != nil {
				bc.logger.Errorf("❌ 启动交易数据管理器失败: %v", err)
			}
		}()
	}

	// 启动盘口数据订阅
	if bc.Config.EnableOrderbookData {
		bc.wg.Add(1)
		go func() {
			defer bc.wg.Done()
			if err := bc.orderbookManager.Start(); err != nil {
				bc.logger.Errorf("❌ 启动盘口数据管理器失败: %v", err)
			}
		}()
	}

	bc.Status.State = "connected"
	bc.logger.Info("✅ Bybit客户端启动成功")
	return nil
}

// Stop 停止Bybit客户端
func (bc *BybitClient) Stop() error {
	bc.logger.Info("🛑 停止Bybit客户端...")

	// 停止数据管理器
	if bc.tradeManager != nil {
		bc.tradeManager.Stop()
	}
	if bc.orderbookManager != nil {
		bc.orderbookManager.Stop()
	}

	// 等待所有goroutine结束
	bc.wg.Wait()

	// 调用基础停止方法
	bc.BaseExchangeClient.Stop()

	bc.logger.Info("✅ Bybit客户端停止完成")
	return nil
}

// SubscribeTradeData 订阅交易数据
func (bc *BybitClient) SubscribeTradeData(symbols []string) error {
	if bc.tradeManager == nil {
		return fmt.Errorf("交易数据管理器未初始化")
	}
	return bc.tradeManager.SubscribeSymbols(symbols)
}

// SubscribeOrderbookData 订阅盘口数据
func (bc *BybitClient) SubscribeOrderbookData(symbols []string) error {
	if bc.orderbookManager == nil {
		return fmt.Errorf("盘口数据管理器未初始化")
	}
	return bc.orderbookManager.SubscribeSymbols(symbols)
}

// SubscribeDepthData 订阅深度数据（暂不实现）
func (bc *BybitClient) SubscribeDepthData(symbols []string) error {
	// Bybit深度数据订阅，这里暂不实现
	return nil
}

// SubscribeFundingData 订阅资金费率数据（暂不实现）
func (bc *BybitClient) SubscribeFundingData(symbols []string) error {
	// Bybit资金费率数据订阅，这里暂不实现
	return nil
}

// StartHeartbeat 启动心跳
func (bc *BybitClient) StartHeartbeat() error {
	// 心跳由各个数据管理器处理
	return nil
}

// StopHeartbeat 停止心跳
func (bc *BybitClient) StopHeartbeat() error {
	// 心跳由各个数据管理器处理
	return nil
}
