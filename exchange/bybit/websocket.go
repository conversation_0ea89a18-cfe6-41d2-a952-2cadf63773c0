package bybit

import (
	"context"
	"encoding/json"
	"exchange_signal/data"
	"fmt"
	"net/url"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gorilla/websocket"
)

// WebSocket配置
const (
	BybitWSURL                = "wss://stream.bybit.com/v5/public/linear"
	BybitPingInterval         = 20 * time.Second
	BybitPongTimeout          = 150 * time.Second
	BybitReconnectDelay       = 3 * time.Second
	BybitMaxReconnectAttempts = 10
)

// 连接状态
type BybitConnectionState int

const (
	BybitDisconnected BybitConnectionState = iota
	BybitConnecting
	BybitConnected
	BybitReconnecting
	BybitStopped
)

// WebSocket连接管理器
type BybitWSConnection struct {
	conn           *websocket.Conn
	connMutex      sync.RWMutex
	state          BybitConnectionState
	stateMutex     sync.RWMutex
	lastPong       time.Time
	pingTicker     *time.Ticker
	reconnectCount int
}

// TradeDataManager 交易数据管理器
type BybitTradeDataManager struct {
	client      *BybitClient
	symbols     []string
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
	connections map[string]*BybitWSConnection
	connMutex   sync.RWMutex
	tradeCount  int64
}

// OrderbookDataManager 盘口数据管理器
type BybitOrderbookDataManager struct {
	client         *BybitClient
	symbols        []string
	ctx            context.Context
	cancel         context.CancelFunc
	wg             sync.WaitGroup
	connections    map[string]*BybitWSConnection
	connMutex      sync.RWMutex
	orderbookCount int64
}

// Bybit订阅消息结构
type BybitSubscriptionMsg struct {
	Op   string   `json:"op"`
	Args []string `json:"args"`
}

// Bybit Ping消息结构
type BybitPingMsg struct {
	Op   string `json:"op"`
	Args []int  `json:"args"`
}

// Bybit交易数据结构
type BybitTradeData struct {
	Topic string `json:"topic"`
	Type  string `json:"type"`
	Data  []struct {
		ExecID       string `json:"execId"`
		Symbol       string `json:"symbol"`
		Price        string `json:"price"`
		Size         string `json:"size"`
		Side         string `json:"side"`
		ExecTime     string `json:"execTime"`
		IsBlockTrade bool   `json:"isBlockTrade"`
	} `json:"data"`
	Ts int64 `json:"ts"`
}

// Bybit盘口数据结构
type BybitBookTickerData struct {
	Topic string `json:"topic"`
	Type  string `json:"type"`
	Data  struct {
		Symbol string     `json:"symbol"`
		Bids   [][]string `json:"bids"`
		Asks   [][]string `json:"asks"`
		Ts     int64      `json:"ts"`
	} `json:"data"`
	Ts int64 `json:"ts"`
}

// 设置连接状态
func (ws *BybitWSConnection) setState(state BybitConnectionState) {
	ws.stateMutex.Lock()
	defer ws.stateMutex.Unlock()
	ws.state = state
}

// 获取连接状态
func (ws *BybitWSConnection) getState() BybitConnectionState {
	ws.stateMutex.RLock()
	defer ws.stateMutex.RUnlock()
	return ws.state
}

// Start 启动交易数据管理器
func (tm *BybitTradeDataManager) Start() error {
	tm.ctx, tm.cancel = context.WithCancel(context.Background())
	tm.connections = make(map[string]*BybitWSConnection)

	tm.client.logger.Info("🚀 启动Bybit交易数据订阅...")

	// 按批次分组订阅
	batchSize := tm.client.Config.BatchSize
	if batchSize <= 0 {
		batchSize = 25
	}

	for i := 0; i < len(tm.symbols); i += batchSize {
		end := i + batchSize
		if end > len(tm.symbols) {
			end = len(tm.symbols)
		}
		batch := tm.symbols[i:end]
		connName := fmt.Sprintf("bybit_trade_batch_%d", i/batchSize+1)

		tm.wg.Add(1)
		go tm.subscribeTradeData(connName, batch)
	}

	return nil
}

// 订阅交易数据
func (tm *BybitTradeDataManager) subscribeTradeData(connName string, symbols []string) {
	defer tm.wg.Done()

	// 构建订阅参数
	var args []string
	for _, symbol := range symbols {
		args = append(args, "publicTrade."+symbol)
	}

	// 带重连的订阅
	tm.subscribeWithRetry(connName, args, tm.handleTradeMessage)
}

// 带重连的订阅
func (tm *BybitTradeDataManager) subscribeWithRetry(connName string, args []string, handler func([]byte)) {
	wsConn := &BybitWSConnection{
		lastPong: time.Now(),
	}

	tm.connMutex.Lock()
	tm.connections[connName] = wsConn
	tm.connMutex.Unlock()

	reconnectAttempts := 0
	currentDelay := BybitReconnectDelay

	for {
		select {
		case <-tm.ctx.Done():
			return
		default:
			if tm.connectAndListen(connName, args, handler, wsConn, &reconnectAttempts) {
				return // 正常退出
			}

			// 重连逻辑
			reconnectAttempts++
			if reconnectAttempts >= BybitMaxReconnectAttempts {
				tm.client.logger.Error("❌ Bybit连接 %s 重连次数超过限制", connName)
				return
			}

			wsConn.setState(BybitReconnecting)
			tm.client.logger.Warn("🔄 Bybit连接 %s 准备重连 (第%d次)，等待 %v", connName, reconnectAttempts, currentDelay)
			time.Sleep(currentDelay)

			// 指数退避
			if currentDelay < 300*time.Second {
				currentDelay *= 2
			}
		}
	}
}

// 连接并监听
func (tm *BybitTradeDataManager) connectAndListen(connName string, args []string, handler func([]byte), wsConn *BybitWSConnection, reconnectAttempts *int) bool {
	wsConn.setState(BybitConnecting)

	// 建立WebSocket连接
	u, _ := url.Parse(BybitWSURL)
	dialer := websocket.DefaultDialer
	dialer.HandshakeTimeout = 10 * time.Second

	conn, _, err := dialer.Dial(u.String(), nil)
	if err != nil {
		tm.client.logger.Error("❌ Bybit WebSocket连接失败 %s: %v", connName, err)
		return false
	}

	wsConn.connMutex.Lock()
	wsConn.conn = conn
	wsConn.connMutex.Unlock()
	wsConn.setState(BybitConnected)

	tm.client.logger.Info("✅ Bybit WebSocket连接成功 %s", connName)

	// 发送订阅消息
	subMsg := BybitSubscriptionMsg{
		Op:   "subscribe",
		Args: args,
	}

	if err := conn.WriteJSON(subMsg); err != nil {
		tm.client.logger.Error("❌ Bybit发送订阅消息失败 %s: %v", connName, err)
		conn.Close()
		return false
	}

	// 启动心跳
	wsConn.pingTicker = time.NewTicker(BybitPingInterval)
	go tm.startPing(conn, connName, wsConn)

	// 监听消息
	for {
		select {
		case <-tm.ctx.Done():
			wsConn.setState(BybitStopped)
			conn.Close()
			return true
		default:
			_, message, err := conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure, websocket.CloseNormalClosure) {
					tm.client.logger.Error("❌ Bybit WebSocket异常关闭 %s: %v", connName, err)
				} else {
					tm.client.logger.Warn("🔌 Bybit WebSocket连接关闭 %s: %v", connName, err)
				}
				wsConn.setState(BybitDisconnected)
				conn.Close()
				if wsConn.pingTicker != nil {
					wsConn.pingTicker.Stop()
				}
				return false
			}

			// 检查pong超时
			if time.Since(wsConn.lastPong) > BybitPongTimeout {
				tm.client.logger.Error("💔 Bybit Pong超时 %s", connName)
				wsConn.setState(BybitDisconnected)
				conn.Close()
				if wsConn.pingTicker != nil {
					wsConn.pingTicker.Stop()
				}
				return false
			}

			handler(message)
		}
	}
}

// 启动心跳 (Bybit使用JSON ping)
func (tm *BybitTradeDataManager) startPing(conn *websocket.Conn, connName string, wsConn *BybitWSConnection) {
	defer wsConn.pingTicker.Stop()

	for {
		select {
		case <-tm.ctx.Done():
			return
		case <-wsConn.pingTicker.C:
			if wsConn.getState() != BybitConnected {
				return
			}

			wsConn.connMutex.Lock()
			if wsConn.conn != nil {
				// Bybit使用JSON ping
				pingMsg := BybitPingMsg{
					Op:   "ping",
					Args: []int{int(time.Now().Unix())},
				}
				err := wsConn.conn.WriteJSON(pingMsg)
				if err != nil {
					tm.client.logger.Error("💔 Bybit发送心跳失败 %s: %v", connName, err)
					wsConn.connMutex.Unlock()
					return
				}
				wsConn.lastPong = time.Now() // Bybit会自动回复pong
			}
			wsConn.connMutex.Unlock()
		}
	}
}

// 处理交易消息
func (tm *BybitTradeDataManager) handleTradeMessage(message []byte) {
	// 检查是否是pong消息
	var result map[string]interface{}
	if err := json.Unmarshal(message, &result); err == nil {
		if op, ok := result["op"]; ok && op == "pong" {
			return
		}
		// 检查是否是订阅确认
		if success, ok := result["success"]; ok && success == true {
			tm.client.logger.Info("✅ Bybit交易数据订阅确认成功")
			return
		}
	}

	// 解析交易数据
	var tradeData BybitTradeData
	if err := json.Unmarshal(message, &tradeData); err != nil {
		return // 忽略无法解析的消息
	}

	if tradeData.Topic != "" && len(tradeData.Data) > 0 {
		for _, trade := range tradeData.Data {
			atomic.AddInt64(&tm.tradeCount, 1)

			// 转换时间戳
			ts, _ := strconv.ParseInt(trade.ExecTime, 10, 64)

			// 处理数据
			tradeDataStruct := &data.TradeData{
				Exchange:  tm.client.Name,
				Symbol:    trade.Symbol,
				TradeID:   trade.ExecID,
				Price:     trade.Price,
				Quantity:  trade.Size,
				Side:      trade.Side,
				TradeTime: ts,
				Timestamp: time.Now().UTC(),
			}
			tm.client.DataHandler.HandleTradeData(tradeDataStruct)
		}
	}
}

// Stop 停止交易数据管理器
func (tm *BybitTradeDataManager) Stop() {
	if tm.cancel != nil {
		tm.cancel()
	}

	// 关闭所有连接
	tm.connMutex.Lock()
	for _, wsConn := range tm.connections {
		wsConn.setState(BybitStopped)
		if wsConn.pingTicker != nil {
			wsConn.pingTicker.Stop()
		}
		wsConn.connMutex.Lock()
		if wsConn.conn != nil {
			wsConn.conn.Close()
		}
		wsConn.connMutex.Unlock()
	}
	tm.connMutex.Unlock()

	tm.wg.Wait()
}

// SubscribeSymbols 订阅交易对
func (tm *BybitTradeDataManager) SubscribeSymbols(symbols []string) error {
	tm.symbols = symbols
	return nil
}

// GetTradeCount 获取交易数据计数
func (tm *BybitTradeDataManager) GetTradeCount() int64 {
	return atomic.LoadInt64(&tm.tradeCount)
}

// Start 启动盘口数据管理器
func (om *BybitOrderbookDataManager) Start() error {
	om.ctx, om.cancel = context.WithCancel(context.Background())
	om.connections = make(map[string]*BybitWSConnection)

	om.client.logger.Info("🚀 启动Bybit盘口数据订阅...")

	// 按批次分组订阅
	batchSize := om.client.Config.BatchSize
	if batchSize <= 0 {
		batchSize = 25
	}

	for i := 0; i < len(om.symbols); i += batchSize {
		end := i + batchSize
		if end > len(om.symbols) {
			end = len(om.symbols)
		}
		batch := om.symbols[i:end]
		connName := fmt.Sprintf("bybit_orderbook_batch_%d", i/batchSize+1)

		om.wg.Add(1)
		go om.subscribeOrderbookData(connName, batch)
	}

	return nil
}

// 订阅盘口数据
func (om *BybitOrderbookDataManager) subscribeOrderbookData(connName string, symbols []string) {
	defer om.wg.Done()

	// 构建订阅参数
	var args []string
	for _, symbol := range symbols {
		args = append(args, "orderbook.1."+symbol)
	}

	// 带重连的订阅
	om.subscribeWithRetry(connName, args, om.handleOrderbookMessage)
}

// 带重连的订阅（复用交易数据的逻辑，但使用不同的处理器）
func (om *BybitOrderbookDataManager) subscribeWithRetry(connName string, args []string, handler func([]byte)) {
	wsConn := &BybitWSConnection{
		lastPong: time.Now(),
	}

	om.connMutex.Lock()
	om.connections[connName] = wsConn
	om.connMutex.Unlock()

	reconnectAttempts := 0
	currentDelay := BybitReconnectDelay

	for {
		select {
		case <-om.ctx.Done():
			return
		default:
			if om.connectAndListen(connName, args, handler, wsConn, &reconnectAttempts) {
				return // 正常退出
			}

			// 重连逻辑
			reconnectAttempts++
			if reconnectAttempts >= BybitMaxReconnectAttempts {
				om.client.logger.Error("❌ Bybit连接 %s 重连次数超过限制", connName)
				return
			}

			wsConn.setState(BybitReconnecting)
			om.client.logger.Warn("🔄 Bybit连接 %s 准备重连 (第%d次)，等待 %v", connName, reconnectAttempts, currentDelay)
			time.Sleep(currentDelay)

			// 指数退避
			if currentDelay < 300*time.Second {
				currentDelay *= 2
			}
		}
	}
}

// 连接并监听（盘口数据）
func (om *BybitOrderbookDataManager) connectAndListen(connName string, args []string, handler func([]byte), wsConn *BybitWSConnection, reconnectAttempts *int) bool {
	wsConn.setState(BybitConnecting)

	// 建立WebSocket连接
	u, _ := url.Parse(BybitWSURL)
	dialer := websocket.DefaultDialer
	dialer.HandshakeTimeout = 10 * time.Second

	conn, _, err := dialer.Dial(u.String(), nil)
	if err != nil {
		om.client.logger.Error("❌ Bybit WebSocket连接失败 %s: %v", connName, err)
		return false
	}

	wsConn.connMutex.Lock()
	wsConn.conn = conn
	wsConn.connMutex.Unlock()
	wsConn.setState(BybitConnected)

	om.client.logger.Info("✅ Bybit WebSocket连接成功 %s", connName)

	// 发送订阅消息
	subMsg := BybitSubscriptionMsg{
		Op:   "subscribe",
		Args: args,
	}

	if err := conn.WriteJSON(subMsg); err != nil {
		om.client.logger.Error("❌ Bybit发送订阅消息失败 %s: %v", connName, err)
		conn.Close()
		return false
	}

	// 启动心跳
	wsConn.pingTicker = time.NewTicker(BybitPingInterval)
	go om.startPing(conn, connName, wsConn)

	// 监听消息
	for {
		select {
		case <-om.ctx.Done():
			wsConn.setState(BybitStopped)
			conn.Close()
			return true
		default:
			_, message, err := conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure, websocket.CloseNormalClosure) {
					om.client.logger.Error("❌ Bybit WebSocket异常关闭 %s: %v", connName, err)
				} else {
					om.client.logger.Warn("🔌 Bybit WebSocket连接关闭 %s: %v", connName, err)
				}
				wsConn.setState(BybitDisconnected)
				conn.Close()
				if wsConn.pingTicker != nil {
					wsConn.pingTicker.Stop()
				}
				return false
			}

			// 检查pong超时
			if time.Since(wsConn.lastPong) > BybitPongTimeout {
				om.client.logger.Error("💔 Bybit Pong超时 %s", connName)
				wsConn.setState(BybitDisconnected)
				conn.Close()
				if wsConn.pingTicker != nil {
					wsConn.pingTicker.Stop()
				}
				return false
			}

			handler(message)
		}
	}
}

// 启动心跳 (盘口数据)
func (om *BybitOrderbookDataManager) startPing(conn *websocket.Conn, connName string, wsConn *BybitWSConnection) {
	defer wsConn.pingTicker.Stop()

	for {
		select {
		case <-om.ctx.Done():
			return
		case <-wsConn.pingTicker.C:
			if wsConn.getState() != BybitConnected {
				return
			}

			wsConn.connMutex.Lock()
			if wsConn.conn != nil {
				// Bybit使用JSON ping
				pingMsg := BybitPingMsg{
					Op:   "ping",
					Args: []int{int(time.Now().Unix())},
				}
				err := wsConn.conn.WriteJSON(pingMsg)
				if err != nil {
					om.client.logger.Error("💔 Bybit发送心跳失败 %s: %v", connName, err)
					wsConn.connMutex.Unlock()
					return
				}
				wsConn.lastPong = time.Now() // Bybit会自动回复pong
			}
			wsConn.connMutex.Unlock()
		}
	}
}

// 处理盘口消息
func (om *BybitOrderbookDataManager) handleOrderbookMessage(message []byte) {
	// 检查是否是pong消息
	var result map[string]interface{}
	if err := json.Unmarshal(message, &result); err == nil {
		if op, ok := result["op"]; ok && op == "pong" {
			return
		}
		// 检查是否是订阅确认
		if success, ok := result["success"]; ok && success == true {
			om.client.logger.Info("✅ Bybit盘口数据订阅确认成功")
			return
		}
	}

	// 解析盘口数据
	var bookData BybitBookTickerData
	if err := json.Unmarshal(message, &bookData); err != nil {
		return // 忽略无法解析的消息
	}

	if bookData.Topic != "" && bookData.Data.Symbol != "" {
		atomic.AddInt64(&om.orderbookCount, 1)

		// 获取最佳买卖价
		var bestBidPrice, bestBidQty, bestAskPrice, bestAskQty string
		if len(bookData.Data.Bids) > 0 && len(bookData.Data.Bids[0]) >= 2 {
			bestBidPrice = bookData.Data.Bids[0][0]
			bestBidQty = bookData.Data.Bids[0][1]
		}
		if len(bookData.Data.Asks) > 0 && len(bookData.Data.Asks[0]) >= 2 {
			bestAskPrice = bookData.Data.Asks[0][0]
			bestAskQty = bookData.Data.Asks[0][1]
		}

		// 处理数据
		orderbookDataStruct := &data.OrderbookData{
			Exchange:     om.client.Name,
			Symbol:       bookData.Data.Symbol,
			BestBidPrice: bestBidPrice,
			BestBidQty:   bestBidQty,
			BestAskPrice: bestAskPrice,
			BestAskQty:   bestAskQty,
			UpdateID:     bookData.Data.Ts,
			Timestamp:    time.Now().UTC(),
		}
		om.client.DataHandler.HandleOrderbookData(orderbookDataStruct)
	}
}

// Stop 停止盘口数据管理器
func (om *BybitOrderbookDataManager) Stop() {
	if om.cancel != nil {
		om.cancel()
	}

	// 关闭所有连接
	om.connMutex.Lock()
	for _, wsConn := range om.connections {
		wsConn.setState(BybitStopped)
		if wsConn.pingTicker != nil {
			wsConn.pingTicker.Stop()
		}
		wsConn.connMutex.Lock()
		if wsConn.conn != nil {
			wsConn.conn.Close()
		}
		wsConn.connMutex.Unlock()
	}
	om.connMutex.Unlock()

	om.wg.Wait()
}

// SubscribeSymbols 订阅交易对
func (om *BybitOrderbookDataManager) SubscribeSymbols(symbols []string) error {
	om.symbols = symbols
	return nil
}

// GetOrderbookCount 获取盘口数据计数
func (om *BybitOrderbookDataManager) GetOrderbookCount() int64 {
	return atomic.LoadInt64(&om.orderbookCount)
}
