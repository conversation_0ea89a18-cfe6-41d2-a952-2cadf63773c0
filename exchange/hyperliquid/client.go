package hyperliquid

import (
	"exchange_signal/exchange"
)

// HyperliquidClient Hyperliquid交易所客户端
type HyperliquidClient struct {
	*exchange.BaseExchangeClient
}

// NewHyperliquidClient 创建新的Hyperliquid客户端
func NewHyperliquidClient() *HyperliquidClient {
	baseClient := exchange.NewBaseExchangeClient("hyperliquid")

	return &HyperliquidClient{
		BaseExchangeClient: baseClient,
	}
}

// Start 启动Hyperliquid客户端（占位符实现）
func (hc *HyperliquidClient) Start() error {
	// TODO: 实现Hyperliquid具体逻辑
	return nil
}
