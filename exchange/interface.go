package exchange

import (
	"context"

	"exchange_signal/config"
	"exchange_signal/data"
)

// ExchangeClient 交易所客户端接口
type ExchangeClient interface {
	// 基础信息
	GetName() string
	GetSymbols() ([]string, error)

	// 连接管理
	Connect(ctx context.Context) error
	Disconnect() error
	IsConnected() bool

	// 数据订阅
	SubscribeTradeData(symbols []string) error
	SubscribeOrderbookData(symbols []string) error
	SubscribeDepthData(symbols []string) error
	SubscribeFundingData(symbols []string) error

	// 心跳机制
	StartHeartbeat() error
	StopHeartbeat() error

	// 状态监控
	GetStatus() *data.ConnectionStatus
	GetStats() *data.DataStats

	// 配置和生命周期
	SetConfig(config *config.ExchangeConfig)
	SetDataHandler(handler data.DataHandler)
	Start() error
	Stop() error
}

// ExchangeFactory 交易所工厂接口
type ExchangeFactory interface {
	CreateClient() ExchangeClient
	GetSupportedExchanges() []string
}

// BaseExchangeClient 基础交易所客户端（提供通用功能）
type BaseExchangeClient struct {
	Name        string
	Config      *config.ExchangeConfig
	DataHandler data.DataHandler
	Ctx         context.Context
	Cancel      context.CancelFunc
	Status      *data.ConnectionStatus
	Stats       *data.DataStats
	Symbols     []string
}

// NewBaseExchangeClient 创建基础交易所客户端
func NewBaseExchangeClient(name string) *BaseExchangeClient {
	ctx, cancel := context.WithCancel(context.Background())

	return &BaseExchangeClient{
		Name:   name,
		Ctx:    ctx,
		Cancel: cancel,
		Status: &data.ConnectionStatus{
			Exchange: name,
			State:    "disconnected",
		},
		Stats: &data.DataStats{
			Exchange: name,
		},
	}
}

// GetName 获取交易所名称
func (b *BaseExchangeClient) GetName() string {
	return b.Name
}

// SetConfig 设置配置
func (b *BaseExchangeClient) SetConfig(config *config.ExchangeConfig) {
	b.Config = config
}

// SetDataHandler 设置数据处理器
func (b *BaseExchangeClient) SetDataHandler(handler data.DataHandler) {
	b.DataHandler = handler
}

// GetStatus 获取连接状态
func (b *BaseExchangeClient) GetStatus() *data.ConnectionStatus {
	return b.Status
}

// GetStats 获取统计信息
func (b *BaseExchangeClient) GetStats() *data.DataStats {
	return b.Stats
}

// GetSymbols 获取交易对（需要子类实现）
func (b *BaseExchangeClient) GetSymbols() ([]string, error) {
	// 这是默认实现，子类应该重写
	return b.Symbols, nil
}

// Connect 连接（需要子类实现）
func (b *BaseExchangeClient) Connect(ctx context.Context) error {
	// 默认实现，子类应该重写
	return nil
}

// Disconnect 断开连接
func (b *BaseExchangeClient) Disconnect() error {
	if b.Cancel != nil {
		b.Cancel()
	}
	b.Status.State = "disconnected"
	return nil
}

// IsConnected 检查连接状态
func (b *BaseExchangeClient) IsConnected() bool {
	return b.Status.State == "connected"
}

// 订阅方法的默认实现（子类应该重写）
func (b *BaseExchangeClient) SubscribeTradeData(symbols []string) error {
	return nil
}

func (b *BaseExchangeClient) SubscribeOrderbookData(symbols []string) error {
	return nil
}

func (b *BaseExchangeClient) SubscribeDepthData(symbols []string) error {
	return nil
}

func (b *BaseExchangeClient) SubscribeFundingData(symbols []string) error {
	return nil
}

// 心跳方法的默认实现（子类应该重写）
func (b *BaseExchangeClient) StartHeartbeat() error {
	return nil
}

func (b *BaseExchangeClient) StopHeartbeat() error {
	return nil
}

// Start 启动客户端（子类应该重写）
func (b *BaseExchangeClient) Start() error {
	return nil
}

// Stop 停止客户端
func (b *BaseExchangeClient) Stop() error {
	return b.Disconnect()
}
