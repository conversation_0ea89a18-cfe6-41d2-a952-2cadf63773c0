package bitget

import (
	"exchange_signal/exchange"
)

// BitgetClient Bitget交易所客户端
type BitgetClient struct {
	*exchange.BaseExchangeClient
}

// NewBitgetClient 创建新的Bitget客户端
func NewBitgetClient() *BitgetClient {
	baseClient := exchange.NewBaseExchangeClient("bitget")

	return &BitgetClient{
		BaseExchangeClient: baseClient,
	}
}

// Start 启动Bitget客户端（占位符实现）
func (bc *BitgetClient) Start() error {
	// TODO: 实现Bitget具体逻辑
	return nil
}
