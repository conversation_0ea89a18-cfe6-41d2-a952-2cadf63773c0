package bitget

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/sirupsen/logrus"

	"exchange_signal/config"
	"exchange_signal/exchange"
)

// BitgetClient Bitget交易所客户端
type BitgetClient struct {
	*exchange.BaseExchangeClient

	// Bitget特定配置
	logger *logrus.Logger

	// WebSocket管理
	tradeManager     *BitgetTradeDataManager
	orderbookManager *BitgetOrderbookDataManager

	// 连接控制
	wg sync.WaitGroup
}

// SymbolInfo Bitget交易对信息
type BitgetSymbolInfo struct {
	Symbol       string `json:"symbol"`
	BaseCoin     string `json:"baseCoin"`
	QuoteCoin    string `json:"quoteCoin"`
	MinTradeNum  string `json:"minTradeNum"`
	MaxTradeNum  string `json:"maxTradeNum"`
	TakerFeeRate string `json:"takerFeeRate"`
	MakerFeeRate string `json:"makerFeeRate"`
	Status       string `json:"status"`
}

// InstrumentsResponse Bitget交易对响应
type BitgetInstrumentsResponse struct {
	Code        string             `json:"code"`
	Msg         string             `json:"msg"`
	RequestTime int64              `json:"requestTime"`
	Data        []BitgetSymbolInfo `json:"data"`
}

// NewBitgetClient 创建新的Bitget客户端
func NewBitgetClient() *BitgetClient {
	baseClient := exchange.NewBaseExchangeClient("bitget")

	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})

	client := &BitgetClient{
		BaseExchangeClient: baseClient,
		logger:             logger,
	}

	return client
}

// SetConfig 设置配置
func (bc *BitgetClient) SetConfig(cfg *config.ExchangeConfig) {
	bc.BaseExchangeClient.SetConfig(cfg)

	// 设置日志级别
	if level, err := logrus.ParseLevel("info"); err == nil {
		bc.logger.SetLevel(level)
	}
}

// GetSymbols 获取所有永续合约交易对
func (bc *BitgetClient) GetSymbols() ([]string, error) {
	bc.logger.Info("🔄 正在获取Bitget永续合约交易对...")

	if bc.Config == nil {
		return nil, fmt.Errorf("配置未设置")
	}

	resp, err := http.Get(bc.Config.APIBaseURL + "/api/mix/v1/market/contracts?productType=umcbl")
	if err != nil {
		return nil, fmt.Errorf("获取交易对信息失败: %v", err)
	}
	defer resp.Body.Close()

	var instrumentsResp BitgetInstrumentsResponse
	if err := json.NewDecoder(resp.Body).Decode(&instrumentsResp); err != nil {
		return nil, fmt.Errorf("解析交易对信息失败: %v", err)
	}

	if instrumentsResp.Code != "00000" {
		return nil, fmt.Errorf("Bitget API错误: %s", instrumentsResp.Msg)
	}

	symbols := make([]string, 0)
	for _, symbol := range instrumentsResp.Data {
		if symbol.Status == "normal" {
			// 应用交易对过滤器
			if bc.shouldIncludeSymbol(symbol.Symbol) {
				symbols = append(symbols, symbol.Symbol)
			}
		}
	}

	bc.Symbols = symbols
	bc.logger.Infof("✅ 成功获取 %d 个Bitget交易对", len(symbols))
	return symbols, nil
}

// shouldIncludeSymbol 检查是否应包含该交易对
func (bc *BitgetClient) shouldIncludeSymbol(symbol string) bool {
	if bc.Config == nil || len(bc.Config.SymbolFilter) == 0 {
		return true // 无过滤器，包含所有
	}

	for _, filter := range bc.Config.SymbolFilter {
		if symbol == filter {
			return true
		}
	}
	return false
}

// Connect 连接到Bitget
func (bc *BitgetClient) Connect(ctx context.Context) error {
	bc.logger.Info("🔗 连接到Bitget...")

	if bc.Config == nil {
		return fmt.Errorf("配置未设置")
	}

	// 获取交易对
	symbols, err := bc.GetSymbols()
	if err != nil {
		return fmt.Errorf("获取交易对失败: %v", err)
	}

	if len(symbols) == 0 {
		return fmt.Errorf("没有可用的交易对")
	}

	// 初始化数据管理器
	bc.tradeManager = &BitgetTradeDataManager{client: bc, symbols: symbols}
	bc.orderbookManager = &BitgetOrderbookDataManager{client: bc, symbols: symbols}

	bc.Status.State = "connected"
	bc.Status.ConnectedTime = time.Now()

	bc.logger.Info("✅ Bitget连接成功")
	return nil
}

// Start 启动Bitget客户端
func (bc *BitgetClient) Start() error {
	if err := bc.Connect(bc.Ctx); err != nil {
		return err
	}

	bc.Status.State = "connecting"
	bc.Stats.StartTime = time.Now()

	// 启动交易数据订阅
	if bc.Config.EnableTradeData {
		bc.wg.Add(1)
		go func() {
			defer bc.wg.Done()
			if err := bc.tradeManager.Start(); err != nil {
				bc.logger.Errorf("❌ 启动交易数据管理器失败: %v", err)
			}
		}()
	}

	// 启动盘口数据订阅
	if bc.Config.EnableOrderbookData {
		bc.wg.Add(1)
		go func() {
			defer bc.wg.Done()
			if err := bc.orderbookManager.Start(); err != nil {
				bc.logger.Errorf("❌ 启动盘口数据管理器失败: %v", err)
			}
		}()
	}

	bc.Status.State = "connected"
	bc.logger.Info("✅ Bitget客户端启动成功")
	return nil
}

// Stop 停止Bitget客户端
func (bc *BitgetClient) Stop() error {
	bc.logger.Info("🛑 停止Bitget客户端...")

	// 停止数据管理器
	if bc.tradeManager != nil {
		bc.tradeManager.Stop()
	}
	if bc.orderbookManager != nil {
		bc.orderbookManager.Stop()
	}

	// 等待所有goroutine结束
	bc.wg.Wait()

	// 调用基础停止方法
	bc.BaseExchangeClient.Stop()

	bc.logger.Info("✅ Bitget客户端停止完成")
	return nil
}

// SubscribeTradeData 订阅交易数据
func (bc *BitgetClient) SubscribeTradeData(symbols []string) error {
	if bc.tradeManager == nil {
		return fmt.Errorf("交易数据管理器未初始化")
	}
	return bc.tradeManager.SubscribeSymbols(symbols)
}

// SubscribeOrderbookData 订阅盘口数据
func (bc *BitgetClient) SubscribeOrderbookData(symbols []string) error {
	if bc.orderbookManager == nil {
		return fmt.Errorf("盘口数据管理器未初始化")
	}
	return bc.orderbookManager.SubscribeSymbols(symbols)
}

// SubscribeDepthData 订阅深度数据（暂不实现）
func (bc *BitgetClient) SubscribeDepthData(symbols []string) error {
	// Bitget深度数据订阅，这里暂不实现
	return nil
}

// SubscribeFundingData 订阅资金费率数据（暂不实现）
func (bc *BitgetClient) SubscribeFundingData(symbols []string) error {
	// Bitget资金费率数据订阅，这里暂不实现
	return nil
}

// StartHeartbeat 启动心跳
func (bc *BitgetClient) StartHeartbeat() error {
	// 心跳由各个数据管理器处理
	return nil
}

// StopHeartbeat 停止心跳
func (bc *BitgetClient) StopHeartbeat() error {
	// 心跳由各个数据管理器处理
	return nil
}
