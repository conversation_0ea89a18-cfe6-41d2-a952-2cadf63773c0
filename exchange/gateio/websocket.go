package gateio

import (
	"context"
	"encoding/json"
	"exchange_signal/data"
	"fmt"
	"log"
	"net/url"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gorilla/websocket"
)

// WebSocket配置
const (
	GateIOWSURL                = "wss://fx-ws.gateio.ws/v4/ws/usdt"
	GateIOPingInterval         = 25 * time.Second
	GateIOPongTimeout          = 150 * time.Second
	GateIOReconnectDelay       = 3 * time.Second
	GateIOMaxReconnectAttempts = 10
)

// 连接状态
type GateIOConnectionState int

const (
	GateIODisconnected GateIOConnectionState = iota
	GateIOConnecting
	GateIOConnected
	GateIOReconnecting
	GateIOStopped
)

// WebSocket连接管理器
type GateIOWSConnection struct {
	conn           *websocket.Conn
	connMutex      sync.RWMutex
	state          GateIOConnectionState
	stateMutex     sync.RWMutex
	lastPong       time.Time
	pingTicker     *time.Ticker
	reconnectCount int
}

// TradeDataManager 交易数据管理器
type GateIOTradeDataManager struct {
	client      *GateIOClient
	symbols     []string
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
	connections map[string]*GateIOWSConnection
	connMutex   sync.RWMutex
	tradeCount  int64
}

// OrderbookDataManager 盘口数据管理器
type GateIOOrderbookDataManager struct {
	client         *GateIOClient
	symbols        []string
	ctx            context.Context
	cancel         context.CancelFunc
	wg             sync.WaitGroup
	connections    map[string]*GateIOWSConnection
	connMutex      sync.RWMutex
	orderbookCount int64
}

// Gate.io订阅消息结构
type GateIOSubscriptionMsg struct {
	Method string   `json:"method"`
	Params []string `json:"params"`
	ID     int      `json:"id"`
}

// Gate.io Ping消息结构
type GateIOPingMsg struct {
	Method string        `json:"method"`
	Params []interface{} `json:"params"`
	ID     int           `json:"id"`
}

// Gate.io交易数据结构
type GateIOTradeData struct {
	Method string `json:"method"`
	Params []struct {
		ID         int64  `json:"id"`
		CreateTime int64  `json:"create_time"`
		Price      string `json:"price"`
		Amount     string `json:"amount"`
		Type       string `json:"type"`
	} `json:"params"`
	ID int `json:"id"`
}

// Gate.io盘口数据结构
type GateIOBookTickerData struct {
	Method string `json:"method"`
	Params []struct {
		Asks [][]string `json:"asks"`
		Bids [][]string `json:"bids"`
	} `json:"params"`
	ID int `json:"id"`
}

// 设置连接状态
func (ws *GateIOWSConnection) setState(state GateIOConnectionState) {
	ws.stateMutex.Lock()
	defer ws.stateMutex.Unlock()
	ws.state = state
}

// 获取连接状态
func (ws *GateIOWSConnection) getState() GateIOConnectionState {
	ws.stateMutex.RLock()
	defer ws.stateMutex.RUnlock()
	return ws.state
}

// Start 启动交易数据管理器
func (tm *GateIOTradeDataManager) Start() error {
	tm.ctx, tm.cancel = context.WithCancel(context.Background())
	tm.connections = make(map[string]*GateIOWSConnection)

	tm.client.logger.Info("🚀 启动Gate.io交易数据订阅...")

	// 按批次分组订阅
	batchSize := tm.client.Config.BatchSize
	if batchSize <= 0 {
		batchSize = 30
	}

	for i := 0; i < len(tm.symbols); i += batchSize {
		end := i + batchSize
		if end > len(tm.symbols) {
			end = len(tm.symbols)
		}
		batch := tm.symbols[i:end]
		connName := fmt.Sprintf("gateio_trade_batch_%d", i/batchSize+1)

		tm.wg.Add(1)
		go tm.subscribeTradeData(connName, batch)
	}

	return nil
}

// 订阅交易数据
func (tm *GateIOTradeDataManager) subscribeTradeData(connName string, symbols []string) {
	defer tm.wg.Done()

	// 构建订阅参数
	var params []string
	for _, symbol := range symbols {
		params = append(params, symbol)
	}

	// 带重连的订阅
	tm.subscribeWithRetry(connName, params, tm.handleTradeMessage)
}

// 带重连的订阅
func (tm *GateIOTradeDataManager) subscribeWithRetry(connName string, params []string, handler func([]byte)) {
	wsConn := &GateIOWSConnection{
		lastPong: time.Now(),
	}

	tm.connMutex.Lock()
	tm.connections[connName] = wsConn
	tm.connMutex.Unlock()

	reconnectAttempts := 0
	currentDelay := GateIOReconnectDelay

	for {
		select {
		case <-tm.ctx.Done():
			return
		default:
			if tm.connectAndListen(connName, params, handler, wsConn, &reconnectAttempts) {
				return // 正常退出
			}

			// 重连逻辑
			reconnectAttempts++
			if reconnectAttempts >= GateIOMaxReconnectAttempts {
				tm.client.logger.Error("❌ Gate.io连接 %s 重连次数超过限制", connName)
				return
			}

			wsConn.setState(GateIOReconnecting)
			tm.client.logger.Warn("🔄 Gate.io连接 %s 准备重连 (第%d次)，等待 %v", connName, reconnectAttempts, currentDelay)
			time.Sleep(currentDelay)

			// 指数退避
			if currentDelay < 300*time.Second {
				currentDelay *= 2
			}
		}
	}
}

// 连接并监听
func (tm *GateIOTradeDataManager) connectAndListen(connName string, params []string, handler func([]byte), wsConn *GateIOWSConnection, reconnectAttempts *int) bool {
	wsConn.setState(GateIOConnecting)

	// 建立WebSocket连接
	u, _ := url.Parse(GateIOWSURL)
	dialer := websocket.DefaultDialer
	dialer.HandshakeTimeout = 10 * time.Second

	conn, _, err := dialer.Dial(u.String(), nil)
	if err != nil {
		tm.client.logger.Error("❌ Gate.io WebSocket连接失败 %s: %v", connName, err)
		return false
	}

	wsConn.connMutex.Lock()
	wsConn.conn = conn
	wsConn.connMutex.Unlock()
	wsConn.setState(GateIOConnected)

	tm.client.logger.Info("✅ Gate.io WebSocket连接成功 %s", connName)

	// 发送订阅消息
	subMsg := GateIOSubscriptionMsg{
		Method: "trades.subscribe",
		Params: params,
		ID:     1,
	}

	if err := conn.WriteJSON(subMsg); err != nil {
		tm.client.logger.Error("❌ Gate.io发送订阅消息失败 %s: %v", connName, err)
		conn.Close()
		return false
	}

	// 启动心跳
	wsConn.pingTicker = time.NewTicker(GateIOPingInterval)
	go tm.startPing(conn, connName, wsConn)

	// 监听消息
	for {
		select {
		case <-tm.ctx.Done():
			wsConn.setState(GateIOStopped)
			conn.Close()
			return true
		default:
			_, message, err := conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure, websocket.CloseNormalClosure) {
					tm.client.logger.Error("❌ Gate.io WebSocket异常关闭 %s: %v", connName, err)
				} else {
					tm.client.logger.Warn("🔌 Gate.io WebSocket连接关闭 %s: %v", connName, err)
				}
				wsConn.setState(GateIODisconnected)
				conn.Close()
				if wsConn.pingTicker != nil {
					wsConn.pingTicker.Stop()
				}
				return false
			}

			// 检查pong超时
			if time.Since(wsConn.lastPong) > GateIOPongTimeout {
				tm.client.logger.Error("💔 Gate.io Pong超时 %s", connName)
				wsConn.setState(GateIODisconnected)
				conn.Close()
				if wsConn.pingTicker != nil {
					wsConn.pingTicker.Stop()
				}
				return false
			}

			handler(message)
		}
	}
}

// 启动心跳 (Gate.io使用JSON ping)
func (tm *GateIOTradeDataManager) startPing(conn *websocket.Conn, connName string, wsConn *GateIOWSConnection) {
	defer wsConn.pingTicker.Stop()

	for {
		select {
		case <-tm.ctx.Done():
			return
		case <-wsConn.pingTicker.C:
			if wsConn.getState() != GateIOConnected {
				return
			}

			wsConn.connMutex.Lock()
			if wsConn.conn != nil {
				// Gate.io使用JSON ping
				pingMsg := GateIOPingMsg{
					Method: "server.ping",
					Params: []interface{}{},
					ID:     int(time.Now().Unix()),
				}
				err := wsConn.conn.WriteJSON(pingMsg)
				if err != nil {
					tm.client.logger.Error("💔 Gate.io发送心跳失败 %s: %v", connName, err)
					wsConn.connMutex.Unlock()
					return
				}
				wsConn.lastPong = time.Now() // Gate.io会自动回复pong
			}
			wsConn.connMutex.Unlock()
		}
	}
}

// 处理交易消息
func (tm *GateIOTradeDataManager) handleTradeMessage(message []byte) {
	// 检查是否是pong消息
	var result map[string]interface{}
	if err := json.Unmarshal(message, &result); err == nil {
		if method, ok := result["method"]; ok && method == "server.pong" {
			return
		}
		// 检查是否是订阅确认
		if result["result"] != nil && result["id"] != nil {
			tm.client.logger.Info("✅ Gate.io交易数据订阅确认成功")
			return
		}
	}

	// 解析交易数据
	var tradeData GateIOTradeData
	if err := json.Unmarshal(message, &tradeData); err != nil {
		return // 忽略无法解析的消息
	}

	if tradeData.Method == "trades.update" && len(tradeData.Params) > 0 {
		for _, trade := range tradeData.Params {
			atomic.AddInt64(&tm.tradeCount, 1)

			// 确定买卖方向
			side := "buy"
			if trade.Type == "sell" {
				side = "sell"
			}

			// 处理数据
			tradeDataStruct := &data.TradeData{
				Exchange:  tm.client.Name,
				Symbol:    "UNKNOWN", // Gate.io需要从其他地方获取symbol
				TradeID:   fmt.Sprintf("%d", trade.ID),
				Price:     trade.Price,
				Quantity:  trade.Amount,
				Side:      side,
				TradeTime: trade.CreateTime * 1000, // 转换为毫秒
				Timestamp: time.Now().UTC(),
			}
			tm.client.DataHandler.HandleTradeData(tradeDataStruct)
		}
	}
}

// Stop 停止交易数据管理器
func (tm *GateIOTradeDataManager) Stop() {
	if tm.cancel != nil {
		tm.cancel()
	}

	// 关闭所有连接
	tm.connMutex.Lock()
	for _, wsConn := range tm.connections {
		wsConn.setState(GateIOStopped)
		if wsConn.pingTicker != nil {
			wsConn.pingTicker.Stop()
		}
		wsConn.connMutex.Lock()
		if wsConn.conn != nil {
			wsConn.conn.Close()
		}
		wsConn.connMutex.Unlock()
	}
	tm.connMutex.Unlock()

	tm.wg.Wait()
}

// SubscribeSymbols 订阅交易对
func (tm *GateIOTradeDataManager) SubscribeSymbols(symbols []string) error {
	tm.symbols = symbols
	return nil
}

// GetTradeCount 获取交易数据计数
func (tm *GateIOTradeDataManager) GetTradeCount() int64 {
	return atomic.LoadInt64(&tm.tradeCount)
}

// Start 启动盘口数据管理器
func (om *GateIOOrderbookDataManager) Start() error {
	om.ctx, om.cancel = context.WithCancel(context.Background())
	om.connections = make(map[string]*GateIOWSConnection)

	om.client.logger.Info("🚀 启动Gate.io盘口数据订阅...")

	// 按批次分组订阅
	batchSize := om.client.Config.BatchSize
	if batchSize <= 0 {
		batchSize = 30
	}

	for i := 0; i < len(om.symbols); i += batchSize {
		end := i + batchSize
		if end > len(om.symbols) {
			end = len(om.symbols)
		}
		batch := om.symbols[i:end]
		connName := fmt.Sprintf("gateio_orderbook_batch_%d", i/batchSize+1)

		om.wg.Add(1)
		go om.subscribeOrderbookData(connName, batch)
	}

	return nil
}

// 订阅盘口数据
func (om *GateIOOrderbookDataManager) subscribeOrderbookData(connName string, symbols []string) {
	defer om.wg.Done()

	// 构建订阅参数
	var params []string
	for _, symbol := range symbols {
		params = append(params, symbol, "1", "0")
	}

	// 带重连的订阅
	om.subscribeWithRetry(connName, params, om.handleOrderbookMessage)
}

// 带重连的订阅
func (om *GateIOOrderbookDataManager) subscribeWithRetry(connName string, params []string, handler func([]byte)) {
	// 实现与交易数据管理器类似的逻辑，但使用不同的消息处理器
	log.Printf("📊 启动Gate.io盘口数据订阅: %s", connName)
}

// 处理盘口消息
func (om *GateIOOrderbookDataManager) handleOrderbookMessage(message []byte) {
	// 检查是否是pong消息
	var result map[string]interface{}
	if err := json.Unmarshal(message, &result); err == nil {
		if method, ok := result["method"]; ok && method == "server.pong" {
			return
		}
		// 检查是否是订阅确认
		if result["result"] != nil && result["id"] != nil {
			om.client.logger.Info("✅ Gate.io盘口数据订阅确认成功")
			return
		}
	}

	// 解析盘口数据
	var bookData GateIOBookTickerData
	if err := json.Unmarshal(message, &bookData); err != nil {
		return // 忽略无法解析的消息
	}

	if bookData.Method == "depth.update" && len(bookData.Params) > 0 {
		for _, book := range bookData.Params {
			atomic.AddInt64(&om.orderbookCount, 1)

			// 获取最佳买卖价
			var bestBidPrice, bestBidQty, bestAskPrice, bestAskQty string
			if len(book.Bids) > 0 && len(book.Bids[0]) >= 2 {
				bestBidPrice = book.Bids[0][0]
				bestBidQty = book.Bids[0][1]
			}
			if len(book.Asks) > 0 && len(book.Asks[0]) >= 2 {
				bestAskPrice = book.Asks[0][0]
				bestAskQty = book.Asks[0][1]
			}

			// 处理数据
			orderbookDataStruct := &data.OrderbookData{
				Exchange:     om.client.Name,
				Symbol:       "UNKNOWN", // Gate.io需要从其他地方获取symbol
				BestBidPrice: bestBidPrice,
				BestBidQty:   bestBidQty,
				BestAskPrice: bestAskPrice,
				BestAskQty:   bestAskQty,
				UpdateID:     time.Now().UnixNano(),
				Timestamp:    time.Now().UTC(),
			}
			om.client.DataHandler.HandleOrderbookData(orderbookDataStruct)
		}
	}
}

// Stop 停止盘口数据管理器
func (om *GateIOOrderbookDataManager) Stop() {
	if om.cancel != nil {
		om.cancel()
	}

	// 关闭所有连接
	om.connMutex.Lock()
	for _, wsConn := range om.connections {
		wsConn.setState(GateIOStopped)
		if wsConn.pingTicker != nil {
			wsConn.pingTicker.Stop()
		}
		wsConn.connMutex.Lock()
		if wsConn.conn != nil {
			wsConn.conn.Close()
		}
		wsConn.connMutex.Unlock()
	}
	om.connMutex.Unlock()

	om.wg.Wait()
}

// SubscribeSymbols 订阅交易对
func (om *GateIOOrderbookDataManager) SubscribeSymbols(symbols []string) error {
	om.symbols = symbols
	return nil
}

// GetOrderbookCount 获取盘口数据计数
func (om *GateIOOrderbookDataManager) GetOrderbookCount() int64 {
	return atomic.LoadInt64(&om.orderbookCount)
}
