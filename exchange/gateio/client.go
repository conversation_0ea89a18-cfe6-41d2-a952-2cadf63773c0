package gateio

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/sirupsen/logrus"

	"exchange_signal/config"
	"exchange_signal/exchange"
)

// GateIOClient Gate.io交易所客户端
type GateIOClient struct {
	*exchange.BaseExchangeClient

	// Gate.io特定配置
	logger *logrus.Logger

	// WebSocket管理
	tradeManager     *GateIOTradeDataManager
	orderbookManager *GateIOOrderbookDataManager

	// 连接控制
	wg sync.WaitGroup
}

// SymbolInfo Gate.io交易对信息
type GateIOSymbolInfo struct {
	Name            string `json:"name"`
	Type            string `json:"type"`
	TradeStatus     string `json:"trade_status"`
	QuantoPrecision int    `json:"quanto_precision"`
}

// ContractsResponse Gate.io合约响应
type GateIOContractsResponse []GateIOSymbolInfo

// NewGateIOClient 创建新的Gate.io客户端
func NewGateIOClient() *GateIOClient {
	baseClient := exchange.NewBaseExchangeClient("gateio")

	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})

	client := &GateIOClient{
		BaseExchangeClient: baseClient,
		logger:             logger,
	}

	return client
}

// SetConfig 设置配置
func (gc *GateIOClient) SetConfig(cfg *config.ExchangeConfig) {
	gc.BaseExchangeClient.SetConfig(cfg)

	// 设置日志级别
	if level, err := logrus.ParseLevel("info"); err == nil {
		gc.logger.SetLevel(level)
	}
}

// GetSymbols 获取所有永续合约交易对
func (gc *GateIOClient) GetSymbols() ([]string, error) {
	gc.logger.Info("🔄 正在获取Gate.io永续合约交易对...")

	if gc.Config == nil {
		return nil, fmt.Errorf("配置未设置")
	}

	resp, err := http.Get(gc.Config.APIBaseURL + "/api/v4/futures/usdt/contracts")
	if err != nil {
		return nil, fmt.Errorf("获取交易对信息失败: %v", err)
	}
	defer resp.Body.Close()

	var contractsResp GateIOContractsResponse
	if err := json.NewDecoder(resp.Body).Decode(&contractsResp); err != nil {
		return nil, fmt.Errorf("解析交易对信息失败: %v", err)
	}

	symbols := make([]string, 0)
	for _, contract := range contractsResp {
		if contract.TradeStatus == "tradable" {
			// 应用交易对过滤器
			if gc.shouldIncludeSymbol(contract.Name) {
				symbols = append(symbols, contract.Name)
			}
		}
	}

	gc.Symbols = symbols
	gc.logger.Infof("✅ 成功获取 %d 个Gate.io交易对", len(symbols))
	return symbols, nil
}

// shouldIncludeSymbol 检查是否应包含该交易对
func (gc *GateIOClient) shouldIncludeSymbol(symbol string) bool {
	if gc.Config == nil || len(gc.Config.SymbolFilter) == 0 {
		return true // 无过滤器，包含所有
	}

	for _, filter := range gc.Config.SymbolFilter {
		if symbol == filter {
			return true
		}
	}
	return false
}

// Connect 连接到Gate.io
func (gc *GateIOClient) Connect(ctx context.Context) error {
	gc.logger.Info("🔗 连接到Gate.io...")

	if gc.Config == nil {
		return fmt.Errorf("配置未设置")
	}

	// 获取交易对
	symbols, err := gc.GetSymbols()
	if err != nil {
		return fmt.Errorf("获取交易对失败: %v", err)
	}

	if len(symbols) == 0 {
		return fmt.Errorf("没有可用的交易对")
	}

	// 初始化数据管理器
	gc.tradeManager = &GateIOTradeDataManager{client: gc, symbols: symbols}
	gc.orderbookManager = &GateIOOrderbookDataManager{client: gc, symbols: symbols}

	gc.Status.State = "connected"
	gc.Status.ConnectedTime = time.Now()

	gc.logger.Info("✅ Gate.io连接成功")
	return nil
}

// Start 启动Gate.io客户端
func (gc *GateIOClient) Start() error {
	if err := gc.Connect(gc.Ctx); err != nil {
		return err
	}

	gc.Status.State = "connecting"
	gc.Stats.StartTime = time.Now()

	// 启动交易数据订阅
	if gc.Config.EnableTradeData {
		gc.wg.Add(1)
		go func() {
			defer gc.wg.Done()
			if err := gc.tradeManager.Start(); err != nil {
				gc.logger.Errorf("❌ 启动交易数据管理器失败: %v", err)
			}
		}()
	}

	// 启动盘口数据订阅
	if gc.Config.EnableOrderbookData {
		gc.wg.Add(1)
		go func() {
			defer gc.wg.Done()
			if err := gc.orderbookManager.Start(); err != nil {
				gc.logger.Errorf("❌ 启动盘口数据管理器失败: %v", err)
			}
		}()
	}

	gc.Status.State = "connected"
	gc.logger.Info("✅ Gate.io客户端启动成功")
	return nil
}

// Stop 停止Gate.io客户端
func (gc *GateIOClient) Stop() error {
	gc.logger.Info("🛑 停止Gate.io客户端...")

	// 停止数据管理器
	if gc.tradeManager != nil {
		gc.tradeManager.Stop()
	}
	if gc.orderbookManager != nil {
		gc.orderbookManager.Stop()
	}

	// 等待所有goroutine结束
	gc.wg.Wait()

	// 调用基础停止方法
	gc.BaseExchangeClient.Stop()

	gc.logger.Info("✅ Gate.io客户端停止完成")
	return nil
}

// SubscribeTradeData 订阅交易数据
func (gc *GateIOClient) SubscribeTradeData(symbols []string) error {
	if gc.tradeManager == nil {
		return fmt.Errorf("交易数据管理器未初始化")
	}
	return gc.tradeManager.SubscribeSymbols(symbols)
}

// SubscribeOrderbookData 订阅盘口数据
func (gc *GateIOClient) SubscribeOrderbookData(symbols []string) error {
	if gc.orderbookManager == nil {
		return fmt.Errorf("盘口数据管理器未初始化")
	}
	return gc.orderbookManager.SubscribeSymbols(symbols)
}

// SubscribeDepthData 订阅深度数据（暂不实现）
func (gc *GateIOClient) SubscribeDepthData(symbols []string) error {
	// Gate.io深度数据订阅，这里暂不实现
	return nil
}

// SubscribeFundingData 订阅资金费率数据（暂不实现）
func (gc *GateIOClient) SubscribeFundingData(symbols []string) error {
	// Gate.io资金费率数据订阅，这里暂不实现
	return nil
}

// StartHeartbeat 启动心跳
func (gc *GateIOClient) StartHeartbeat() error {
	// 心跳由各个数据管理器处理
	return nil
}

// StopHeartbeat 停止心跳
func (gc *GateIOClient) StopHeartbeat() error {
	// 心跳由各个数据管理器处理
	return nil
}
