# 统一交易所永续合约数据订阅系统

一个高性能、高稳定性的**统一多交易所**永续合约数据订阅系统，支持实时获取交易数据和盘口数据。

## 🏗️ 系统架构

### 整体架构设计

本系统采用**统一架构模式**，将原来分散的6个交易所程序整合成一个统一的系统：

```
┌─────────────────────────────────────────────────────────────┐
│                    统一交易所数据订阅系统                    │
├─────────────────────────────────────────────────────────────┤
│                       主程序入口                           │
│                      (main.go)                            │
├─────────────────────────────────────────────────────────────┤
│                   交易所管理器                              │
│                  (ExchangeManager)                        │
├─────────────────────────────────────────────────────────────┤
│  Binance  │  OKX  │ Gate.io │ Bybit │ Bitget │ Hyperliquid │
│  Client   │ Client│ Client  │Client │ Client │   Client    │
├─────────────────────────────────────────────────────────────┤
│                   统一数据处理器                            │
│                  (DataHandler)                            │
├─────────────────────────────────────────────────────────────┤
│     统一配置管理     │     统一日志系统    │   统一监控     │
└─────────────────────────────────────────────────────────────┘
```

### 核心架构特点

1. **统一管理**
   - 单一程序入口，统一启动所有交易所
   - 统一配置文件管理所有交易所参数
   - 统一日志系统和监控面板

2. **模块化设计**
   - 每个交易所独立实现 `ExchangeClient` 接口
   - 统一的数据类型和处理流程
   - 可插拔的数据处理器（控制台、文件、Kafka等）

3. **高可扩展性**
   - 新增交易所只需实现统一接口
   - 支持动态启用/禁用交易所
   - 支持不同的数据订阅组合

## 📂 项目结构

```
exchange_signal/
├── main.go                 # 统一程序入口
├── config.json             # 统一配置文件
├── start.sh               # 启动脚本
├── Makefile               # 构建脚本
├── config/
│   └── config.go          # 配置管理
├── data/
│   ├── types.go           # 统一数据类型
│   └── handler.go         # 数据处理器
├── exchange/
│   ├── interface.go       # 交易所接口定义
│   ├── binance/
│   │   ├── client.go      # Binance实现
│   │   └── websocket.go   # WebSocket管理
│   ├── okx/
│   │   └── client.go      # OKX实现
│   ├── gateio/
│   │   └── client.go      # Gate.io实现
│   ├── bybit/
│   │   └── client.go      # Bybit实现
│   ├── bitget/
│   │   └── client.go      # Bitget实现
│   └── hyperliquid/
│       └── client.go      # Hyperliquid实现
├── manager/
│   └── manager.go         # 交易所管理器
└── logs/                  # 日志目录
```

## 🚀 快速开始

### 1. 环境要求

- Go 1.21+
- 网络连接（访问各交易所API）

### 2. 安装和配置

```bash
# 克隆项目
git clone <项目地址>
cd exchange_signal

# 初始化项目
make init

# 构建程序
make build
```

### 3. 配置文件

编辑 `config.json` 配置各交易所参数：

```json
{
  "log_level": "info",
  "data_handler": "console",
  "exchanges": {
    "binance": {
      "enabled": true,
      "enable_trade_data": true,
      "enable_orderbook_data": true,
      "symbol_filter": []
    },
    "okx": {
      "enabled": false,
      ...
    }
  }
}
```

### 4. 启动系统

```bash
# 启动程序
./start.sh start

# 或使用 Makefile
make start
```

### 5. 管理程序

```bash
# 查看状态
./start.sh status

# 查看日志
./start.sh logs

# 停止程序
./start.sh stop

# 重启程序
./start.sh restart
```

## 📋 可用命令

### 启动脚本命令

```bash
./start.sh start     # 启动程序
./start.sh stop      # 停止程序
./start.sh restart   # 重启程序
./start.sh status    # 查看状态
./start.sh logs      # 查看实时日志
./start.sh build     # 构建程序
./start.sh clean     # 清理文件
./start.sh help      # 显示帮助
```

### Makefile命令

```bash
make build     # 构建程序
make start     # 启动程序
make stop      # 停止程序
make restart   # 重启程序
make status    # 查看状态
make logs      # 查看日志
make clean     # 清理文件
make test      # 运行测试
make check     # 完整检查（格式化+检查+测试+构建）
```

## 🔧 配置说明

### 全局配置

- `log_level`: 日志级别 (debug, info, warn, error)
- `data_handler`: 数据处理器类型 (console, json, kafka)
- `stats_interval`: 统计信息输出间隔
- `monitor_interval`: 状态监控间隔

### 交易所配置

每个交易所支持独立配置：

- `enabled`: 是否启用该交易所
- `batch_size`: 批次大小（每个WebSocket连接处理的交易对数量）
- `heartbeat_type`: 心跳类型 (websocket_ping, text_ping, json_ping)
- `enable_trade_data`: 是否订阅交易数据
- `enable_orderbook_data`: 是否订阅盘口数据
- `symbol_filter`: 交易对过滤器（空数组表示订阅所有）

### 支持的交易所

| 交易所 | 状态 | 批次大小 | 心跳类型 | 特殊说明 |
|--------|------|----------|----------|----------|
| Binance | ✅ 已实现 | 20 | WebSocket Ping | 完整实现 |
| OKX | 🔄 待实现 | 20 | Text Ping | 占位符 |
| Gate.io | 🔄 待实现 | 30 | JSON Ping | 占位符 |
| Bybit | 🔄 待实现 | 15 | JSON Ping | 占位符 |
| Bitget | 🔄 待实现 | 20 | Text Ping | 占位符 |
| Hyperliquid | 🔄 待实现 | 25 | WebSocket Ping | 占位符 |

## 📊 数据格式

### 统一交易数据格式

```json
{
  "exchange": "binance",
  "symbol": "BTCUSDT",
  "trade_id": "123456",
  "price": "50000.00",
  "quantity": "0.001",
  "side": "buy",
  "trade_time": 1640995200000,
  "timestamp": "2022-01-01T00:00:00Z"
}
```

### 统一盘口数据格式

```json
{
  "exchange": "binance",
  "symbol": "BTCUSDT",
  "best_bid_price": "49999.99",
  "best_bid_qty": "1.5",
  "best_ask_price": "50000.01",
  "best_ask_qty": "2.0",
  "update_id": 789012,
  "timestamp": "2022-01-01T00:00:00Z"
}
```

## 🔍 监控和日志

### 状态监控

系统提供实时状态监控：

- 连接状态：各交易所连接状态
- 数据统计：实时数据接收统计
- 性能指标：连接质量、延迟等
- 错误监控：连接错误、数据解析错误等

### 日志系统

- 统一日志格式
- 分级日志输出
- 自动日志轮转
- 实时日志查看

## 🛠️ 开发指南

### 添加新交易所

1. 在 `exchange/` 目录下创建新的交易所包
2. 实现 `ExchangeClient` 接口
3. 在管理器中注册新的交易所工厂
4. 在配置文件中添加相应配置

### 自定义数据处理器

1. 实现 `DataHandler` 接口
2. 在配置中指定处理器类型
3. 支持输出到文件、数据库、消息队列等

## 📝 更新日志

### v2.0.0 (统一架构)

- ✅ 整合6个交易所为统一程序
- ✅ 统一配置文件管理
- ✅ 统一数据类型和处理流程
- ✅ 简化启动脚本和Makefile
- ✅ 完整的Binance实现
- 🔄 其他交易所待完善实现

### v1.0.0 (分散架构)

- ✅ 独立的6个交易所程序
- ✅ 各自独立的配置和管理
- ✅ 基础的数据订阅功能

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！ 