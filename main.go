package main

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"exchange_signal/config"
	"exchange_signal/manager"
)

func main() {
	// 创建必要的目录
	if err := createDirectories(); err != nil {
		fmt.Printf("❌ 创建目录失败: %v\n", err)
		os.Exit(1)
	}

	// 加载配置
	cfg, err := config.LoadConfig("config.json")
	if err != nil {
		fmt.Printf("❌ 加载配置失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("🏗️  交易所永续合约数据订阅系统")
	fmt.Println("🚀 启动中...")

	// 创建交易所管理器
	manager, err := manager.NewExchangeManager(cfg)
	if err != nil {
		fmt.Printf("❌ 创建管理器失败: %v\n", err)
		os.Exit(1)
	}

	// 启动所有交易所
	if err := manager.Start(); err != nil {
		fmt.Printf("❌ 启动失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("✅ 系统启动成功")
	fmt.Println("📊 数据订阅已开始...")
	fmt.Println("按 Ctrl+C 停止程序")

	// 等待退出信号
	waitForShutdown(manager)
}

// createDirectories 创建必要的目录
func createDirectories() error {
	dirs := []string{"logs", "data", "config"}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录 %s 失败: %v", dir, err)
		}
	}

	return nil
}

// waitForShutdown 等待关闭信号
func waitForShutdown(manager *manager.ExchangeManager) {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待信号
	sig := <-sigChan
	fmt.Printf("\n🛑 收到停止信号: %v\n", sig)
	fmt.Println("🔄 正在停止所有交易所...")

	// 优雅关闭
	if err := manager.Stop(); err != nil {
		fmt.Printf("❌ 停止过程中出错: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("✅ 程序已安全退出")
}
