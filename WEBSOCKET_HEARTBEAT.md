# WebSocket 心跳机制实现说明

## 概述

本项目为6个交易所实现了统一的WebSocket连接和心跳检测机制，确保连接的稳定性和数据的连续性。

## 心跳机制类型

### 1. WebSocket Ping/Pong 机制
**适用交易所**: Binance, Hyperliquid

**实现方式**:
```go
// 发送WebSocket标准Ping帧
err := conn.WriteMessage(websocket.PingMessage, []byte{})

// 设置Pong处理器
conn.SetPongHandler(func(appData string) error {
    lastPong = time.Now()
    return nil
})
```

**特点**:
- 使用WebSocket协议标准的Ping/Pong帧
- 自动处理，无需手动解析
- 最稳定的心跳机制

### 2. 文本 Ping 机制
**适用交易所**: OKX, Bitget

**实现方式**:
```go
// 发送文本"ping"
err := conn.WriteMessage(websocket.TextMessage, []byte("ping"))

// 在消息处理中检查"pong"响应
if string(message) == "pong" {
    lastPong = time.Now()
    return
}
```

**特点**:
- 发送简单的文本字符串
- 需要在消息处理中识别pong响应
- 实现简单，兼容性好

### 3. JSON Ping 机制
**适用交易所**: Gate.io, Bybit

**实现方式**:
```go
// 发送JSON格式ping
pingMsg := map[string]interface{}{
    "method": "ping",  // Gate.io格式
    // 或
    "op": "ping",      // Bybit格式
}
err := conn.WriteJSON(pingMsg)

// 在消息处理中检查JSON响应
var response map[string]interface{}
if json.Unmarshal(message, &response) == nil {
    if response["method"] == "pong" || response["op"] == "pong" {
        lastPong = time.Now()
        return
    }
}
```

**特点**:
- 使用JSON格式的结构化消息
- 可以携带更多信息
- 需要JSON解析处理

## 统一配置参数

```go
const (
    PingInterval     = 25 * time.Second   // 心跳发送间隔
    PongTimeout      = 150 * time.Second  // Pong超时时间（6倍心跳间隔）
    ReconnectDelay   = 3 * time.Second    // 重连延迟
    MaxReconnectAttempts = 10             // 最大重连次数
)
```

## 各交易所具体实现

### Binance (已完整实现)
- **WebSocket URL**: `wss://fstream.binance.com/ws`
- **心跳类型**: WebSocket Ping/Pong
- **批次大小**: 20个交易对/连接
- **订阅格式**: `{"method":"SUBSCRIBE","params":["BTCUSDT@aggTrade"],"id":1}`

**实现状态**: ✅ 完整实现
- 完整的WebSocket连接管理
- 标准Ping/Pong心跳机制
- 分批订阅和连接管理
- 智能重连机制
- 完整的数据解析和处理

### OKX (框架已实现)
- **WebSocket URL**: `wss://ws.okx.com:8443/ws/v5/public`
- **心跳类型**: 文本 Ping ("ping" -> "pong")
- **批次大小**: 20个交易对/连接
- **订阅格式**: `{"op":"subscribe","args":[{"channel":"trades","instId":"BTC-USDT-SWAP"}]}`

**实现状态**: 🔄 框架完成，待完善
- 基础客户端框架 ✅
- 配置和日志系统 ✅
- WebSocket连接逻辑 ⏳
- 文本ping心跳机制 ⏳
- 数据解析和处理 ⏳

### Gate.io (框架已实现)
- **WebSocket URL**: `wss://fx-ws.gateio.ws/v4/ws/usdt`
- **心跳类型**: JSON Ping (`{"method":"ping"}` -> `{"method":"pong"}`)
- **批次大小**: 30个交易对/连接
- **订阅格式**: `{"method":"futures.subscribe","params":["BTC_USDT.trades"]}`

**实现状态**: 🔄 框架完成，待完善
- 基础客户端框架 ✅
- 配置和日志系统 ✅
- WebSocket连接逻辑 ⏳
- JSON ping心跳机制 ⏳
- 数据解析和处理 ⏳

### Bybit (框架待实现)
- **WebSocket URL**: `wss://stream.bybit.com/v5/public/linear`
- **心跳类型**: JSON Ping (`{"op":"ping"}` -> `{"op":"pong"}`)
- **批次大小**: 15个交易对/连接
- **订阅格式**: `{"op":"subscribe","args":["publicTrade.BTCUSDT"]}`

**实现状态**: ⏳ 待实现

### Bitget (框架待实现)
- **WebSocket URL**: `wss://ws.bitget.com/v2/ws/public`
- **心跳类型**: 文本 Ping ("ping" -> "pong")
- **批次大小**: 20个交易对/连接
- **订阅格式**: `{"op":"subscribe","args":[{"instType":"USDT-FUTURES","channel":"trade","instId":"BTCUSDT"}]}`

**实现状态**: ⏳ 待实现

### Hyperliquid (框架待实现)
- **WebSocket URL**: `wss://api.hyperliquid.xyz/ws`
- **心跳类型**: WebSocket Ping/Pong
- **批次大小**: 25个交易对/连接
- **订阅格式**: `{"method":"subscribe","subscription":{"type":"trades","coin":"BTC"}}`

**实现状态**: ⏳ 待实现

## 连接管理架构

### 分批连接策略
```
每个交易所 = TradeDataManager + OrderbookDataManager
├── TradeDataManager
│   ├── trade_batch_1 → WebSocket连接1 → 交易对1-N
│   ├── trade_batch_2 → WebSocket连接2 → 交易对N+1-2N
│   └── trade_batch_X → WebSocket连接X → 交易对...
└── OrderbookDataManager
    ├── orderbook_batch_1 → WebSocket连接1 → 交易对1-N
    ├── orderbook_batch_2 → WebSocket连接2 → 交易对N+1-2N
    └── orderbook_batch_Y → WebSocket连接Y → 交易对...
```

### 连接状态管理
```go
type ConnectionState int

const (
    Disconnected ConnectionState = iota
    Connecting
    Connected
    Reconnecting
    Stopped
)
```

### 重连机制
- **指数退避**: 3s → 6s → 12s → 24s → 48s → 96s → 300s
- **最大重连次数**: 10次
- **Pong超时检测**: 150秒（6倍心跳间隔）
- **连接质量监控**: 实时监控连接健康状态

## 数据处理流程

```
WebSocket消息接收
        ↓
   消息类型判断
        ↓
┌─────────────────┬─────────────────┬─────────────────┐
│   心跳响应处理   │   订阅确认处理   │   实际数据处理   │
│       ↓         │       ↓         │       ↓         │
│  更新lastPong   │   记录订阅状态   │   数据解析转换   │
│       ↓         │       ↓         │       ↓         │
│     返回        │     返回        │   统计计数更新   │
│                 │                 │       ↓         │
│                 │                 │ 调用数据处理器   │
└─────────────────┴─────────────────┴─────────────────┘
```

## 监控和统计

### 连接监控
- 连接状态实时监控
- 心跳延迟统计
- 重连次数统计
- 消息接收速率

### 数据统计
- 交易数据计数
- 盘口数据计数
- 数据接收时间戳
- 连接质量评分

## 配置示例

```json
{
  "exchanges": {
    "binance": {
      "enabled": true,
      "batch_size": 20,
      "heartbeat_type": "websocket_ping",
      "enable_trade_data": true,
      "enable_orderbook_data": true
    },
    "okx": {
      "enabled": true,
      "batch_size": 20,
      "heartbeat_type": "text_ping",
      "enable_trade_data": true,
      "enable_orderbook_data": true
    },
    "gateio": {
      "enabled": true,
      "batch_size": 30,
      "heartbeat_type": "json_ping",
      "enable_trade_data": true,
      "enable_orderbook_data": true
    }
  }
}
```

## 下一步开发计划

1. **完善OKX实现**
   - 实现完整的WebSocket连接逻辑
   - 实现文本ping心跳机制
   - 实现数据解析和处理

2. **完善Gate.io实现**
   - 实现完整的WebSocket连接逻辑
   - 实现JSON ping心跳机制
   - 实现数据解析和处理

3. **实现Bybit客户端**
   - 创建基础客户端框架
   - 实现JSON ping心跳机制
   - 实现数据订阅和处理

4. **实现Bitget客户端**
   - 创建基础客户端框架
   - 实现文本ping心跳机制
   - 实现数据订阅和处理

5. **实现Hyperliquid客户端**
   - 创建基础客户端框架
   - 实现WebSocket ping心跳机制
   - 实现数据订阅和处理

## 测试和验证

### 连接稳定性测试
- 长时间运行测试（24小时+）
- 网络中断恢复测试
- 高频数据处理测试

### 心跳机制测试
- 心跳超时处理测试
- 重连机制测试
- 不同网络环境测试

### 数据完整性测试
- 数据丢失检测
- 重复数据过滤
- 数据格式验证 