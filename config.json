{"log_level": "info", "data_handler": "console", "log_file": "logs/exchange_signal.log", "stats_interval": "30s", "monitor_interval": "10s", "exchanges": {"binance": {"enabled": true, "api_base_url": "https://fapi.binance.com", "ws_base_url": "wss://fstream.binance.com/ws/", "batch_size": 100, "max_reconnect_attempts": 15, "reconnect_delay": "1s", "reconnect_strategy": "exponential_backoff", "max_reconnect_delay": "60s", "ping_interval": "180s", "pong_timeout": "10s", "connection_timeout": "15s", "read_timeout": "300s", "write_timeout": "10s", "heartbeat_type": "websocket_ping", "enable_trade_data": true, "enable_orderbook_data": true, "enable_depth_data": false, "enable_funding_data": true, "symbol_filter": [], "stability_config": {"max_connections_per_ip": 50, "max_subscriptions_per_connection": 100, "rate_limit_per_minute": 1200, "connection_health_check_interval": "60s", "aggressive_reconnect": true, "error_threshold": 5}}, "okx": {"enabled": true, "api_base_url": "https://www.okx.com", "ws_base_url": "wss://ws.okx.com:8443/ws/v5/public", "batch_size": 40, "max_reconnect_attempts": 12, "reconnect_delay": "3s", "reconnect_strategy": "exponential_backoff", "max_reconnect_delay": "300s", "ping_interval": "25s", "pong_timeout": "35s", "connection_timeout": "12s", "read_timeout": "90s", "write_timeout": "8s", "heartbeat_type": "text_ping", "enable_trade_data": true, "enable_orderbook_data": true, "enable_depth_data": false, "enable_funding_data": true, "symbol_filter": [], "stability_config": {"max_connections_per_ip": 30, "max_subscriptions_per_connection": 40, "rate_limit_per_minute": 600, "connection_health_check_interval": "45s", "aggressive_reconnect": false, "error_threshold": 3, "peak_hour_batch_reduction": 0.8}}, "gateio": {"enabled": true, "api_base_url": "https://api.gateio.ws", "ws_base_url": "wss://fx-ws.gateio.ws/v4/ws/usdt", "batch_size": 20, "max_reconnect_attempts": 8, "reconnect_delay": "8s", "reconnect_strategy": "linear_backoff", "max_reconnect_delay": "120s", "ping_interval": "25s", "pong_timeout": "40s", "connection_timeout": "15s", "read_timeout": "75s", "write_timeout": "12s", "heartbeat_type": "json_ping", "enable_trade_data": true, "enable_orderbook_data": true, "enable_depth_data": false, "enable_funding_data": true, "symbol_filter": [], "stability_config": {"max_connections_per_ip": 15, "max_subscriptions_per_connection": 20, "rate_limit_per_minute": 300, "connection_health_check_interval": "90s", "aggressive_reconnect": false, "error_threshold": 2, "conservative_mode": true}}, "bybit": {"enabled": true, "api_base_url": "https://api.bybit.com", "ws_base_url": "wss://stream.bybit.com/v5/public/linear", "batch_size": 25, "max_reconnect_attempts": 15, "reconnect_delay": "2s", "reconnect_strategy": "fast_exponential_backoff", "max_reconnect_delay": "90s", "ping_interval": "20s", "pong_timeout": "15s", "connection_timeout": "8s", "read_timeout": "45s", "write_timeout": "6s", "heartbeat_type": "json_ping", "enable_trade_data": true, "enable_orderbook_data": true, "enable_depth_data": false, "enable_funding_data": true, "symbol_filter": [], "stability_config": {"max_connections_per_ip": 40, "max_subscriptions_per_connection": 25, "rate_limit_per_minute": 800, "connection_health_check_interval": "30s", "aggressive_reconnect": true, "error_threshold": 4, "peak_hour_handling": "reduce_batch_size"}}, "bitget": {"enabled": true, "api_base_url": "https://api.bitget.com", "ws_base_url": "wss://ws.bitget.com/mix/v1/stream", "batch_size": 30, "max_reconnect_attempts": 10, "reconnect_delay": "5s", "reconnect_strategy": "cautious_exponential_backoff", "max_reconnect_delay": "180s", "ping_interval": "30s", "pong_timeout": "45s", "connection_timeout": "12s", "read_timeout": "70s", "write_timeout": "10s", "heartbeat_type": "text_ping", "enable_trade_data": true, "enable_orderbook_data": true, "enable_depth_data": false, "enable_funding_data": true, "symbol_filter": [], "stability_config": {"max_connections_per_ip": 25, "max_subscriptions_per_connection": 30, "rate_limit_per_minute": 400, "connection_health_check_interval": "75s", "aggressive_reconnect": false, "error_threshold": 3, "cautious_mode": true, "startup_delay": "10s"}}}}