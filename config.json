{"log_level": "info", "data_handler": "console", "log_file": "logs/exchange_signal.log", "stats_interval": "30s", "monitor_interval": "10s", "exchanges": {"binance": {"enabled": true, "api_base_url": "https://fapi.binance.com", "ws_base_url": "wss://fstream.binance.com/ws/", "batch_size": 100, "max_reconnect_attempts": 10, "reconnect_delay": "5s", "reconnect_strategy": "infinite", "max_reconnect_delay": "5m", "ping_interval": "25s", "pong_timeout": "150s", "connection_timeout": "10s", "read_timeout": "60s", "write_timeout": "10s", "heartbeat_type": "websocket_ping", "enable_trade_data": true, "enable_orderbook_data": true, "enable_depth_data": false, "enable_funding_data": true, "symbol_filter": []}, "okx": {"enabled": true, "api_base_url": "https://www.okx.com", "ws_base_url": "wss://ws.okx.com:8443/ws/v5/public", "batch_size": 50, "max_reconnect_attempts": 10, "reconnect_delay": "5s", "reconnect_strategy": "infinite", "max_reconnect_delay": "5m", "ping_interval": "25s", "pong_timeout": "150s", "connection_timeout": "10s", "read_timeout": "60s", "write_timeout": "10s", "heartbeat_type": "text_ping", "enable_trade_data": true, "enable_orderbook_data": true, "enable_depth_data": false, "enable_funding_data": true, "symbol_filter": []}, "gateio": {"enabled": true, "api_base_url": "https://api.gateio.ws", "ws_base_url": "wss://api.gateio.ws/ws/v4/", "batch_size": 50, "max_reconnect_attempts": 10, "reconnect_delay": "5s", "reconnect_strategy": "infinite", "max_reconnect_delay": "5m", "ping_interval": "25s", "pong_timeout": "150s", "connection_timeout": "10s", "read_timeout": "60s", "write_timeout": "10s", "heartbeat_type": "json_ping", "enable_trade_data": true, "enable_orderbook_data": true, "enable_depth_data": false, "enable_funding_data": true, "symbol_filter": []}, "bybit": {"enabled": true, "api_base_url": "https://api.bybit.com", "ws_base_url": "wss://stream.bybit.com/v5/public/linear", "batch_size": 15, "max_reconnect_attempts": 10, "reconnect_delay": "5s", "reconnect_strategy": "infinite", "max_reconnect_delay": "5m", "ping_interval": "25s", "pong_timeout": "150s", "connection_timeout": "10s", "read_timeout": "60s", "write_timeout": "10s", "heartbeat_type": "json_ping", "enable_trade_data": true, "enable_orderbook_data": true, "enable_depth_data": false, "enable_funding_data": true, "symbol_filter": []}, "bitget": {"enabled": true, "api_base_url": "https://api.bitget.com", "ws_base_url": "wss://ws.bitget.com/mix/v1/stream", "batch_size": 50, "max_reconnect_attempts": 10, "reconnect_delay": "5s", "reconnect_strategy": "infinite", "max_reconnect_delay": "5m", "ping_interval": "25s", "pong_timeout": "150s", "connection_timeout": "10s", "read_timeout": "60s", "write_timeout": "10s", "heartbeat_type": "text_ping", "enable_trade_data": true, "enable_orderbook_data": true, "enable_depth_data": false, "enable_funding_data": true, "symbol_filter": []}}}