#!/bin/bash

# 统一交易所数据订阅系统启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 程序名称
PROGRAM_NAME="exchange-signal"
PID_FILE="logs/${PROGRAM_NAME}.pid"
LOG_FILE="logs/${PROGRAM_NAME}.log"

# 函数：打印彩色消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：检查程序是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# 函数：启动程序
start() {
    print_message $CYAN "🏗️  统一交易所数据订阅系统"
    echo ""
    
    if is_running; then
        print_message $YELLOW "⚠️  程序已经在运行中"
        status
        return 1
    fi
    
    print_message $BLUE "🚀 启动程序..."
    
    # 确保目录存在
    mkdir -p logs data config
    
    # 构建程序
    print_message $BLUE "🔨 构建程序..."
    go build -o "$PROGRAM_NAME" .
    
    if [ $? -ne 0 ]; then
        print_message $RED "❌ 构建失败"
        return 1
    fi
    
    # 启动程序
    nohup ./"$PROGRAM_NAME" > "$LOG_FILE" 2>&1 &
    local pid=$!
    echo $pid > "$PID_FILE"
    
    sleep 2
    
    if is_running; then
        print_message $GREEN "✅ 程序启动成功 (PID: $pid)"
        print_message $CYAN "📊 数据订阅已开始..."
        print_message $YELLOW "📋 查看日志: tail -f $LOG_FILE"
        print_message $YELLOW "📊 查看状态: ./start.sh status"
    else
        print_message $RED "❌ 程序启动失败"
        return 1
    fi
}

# 函数：停止程序
stop() {
    print_message $BLUE "🛑 停止程序..."
    
    if ! is_running; then
        print_message $YELLOW "⚠️  程序未运行"
        return 1
    fi
    
    local pid=$(cat "$PID_FILE")
    kill "$pid"
    
    # 等待程序停止
    local count=0
    while is_running && [ $count -lt 10 ]; do
        sleep 1
        count=$((count + 1))
    done
    
    if is_running; then
        print_message $YELLOW "⚠️  强制停止程序..."
        kill -9 "$pid"
        sleep 1
    fi
    
    rm -f "$PID_FILE"
    print_message $GREEN "✅ 程序已停止"
}

# 函数：重启程序
restart() {
    print_message $BLUE "🔄 重启程序..."
    stop
    sleep 2
    start
}

# 函数：查看状态
status() {
    print_message $CYAN "📊 程序状态检查"
    echo ""
    
    if is_running; then
        local pid=$(cat "$PID_FILE")
        print_message $GREEN "✅ 程序正在运行 (PID: $pid)"
        
        # 显示进程信息
        ps -p "$pid" -o pid,ppid,pcpu,pmem,etime,cmd
        
        # 显示最近的日志
        if [ -f "$LOG_FILE" ]; then
            echo ""
            print_message $CYAN "📋 最近日志 (最后10行):"
            tail -10 "$LOG_FILE"
        fi
    else
        print_message $RED "❌ 程序未运行"
    fi
}

# 函数：查看日志
logs() {
    if [ -f "$LOG_FILE" ]; then
        print_message $CYAN "📋 实时日志 (按Ctrl+C退出):"
        tail -f "$LOG_FILE"
    else
        print_message $YELLOW "⚠️  日志文件不存在: $LOG_FILE"
    fi
}

# 函数：构建程序
build() {
    print_message $BLUE "🔨 构建程序..."
    go mod tidy
    go build -o "$PROGRAM_NAME" .
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 构建成功"
    else
        print_message $RED "❌ 构建失败"
        return 1
    fi
}

# 函数：清理
clean() {
    print_message $BLUE "🧹 清理文件..."
    
    if is_running; then
        print_message $YELLOW "⚠️  程序正在运行，先停止程序"
        stop
    fi
    
    rm -f "$PROGRAM_NAME"
    rm -f "$PID_FILE"
    print_message $GREEN "✅ 清理完成"
}

# 函数：显示帮助
show_help() {
    print_message $CYAN "🏗️  统一交易所数据订阅系统"
    echo ""
    echo "用法: $0 {start|stop|restart|status|logs|build|clean|help}"
    echo ""
    echo "命令说明:"
    echo "  start   - 启动程序"
    echo "  stop    - 停止程序"
    echo "  restart - 重启程序"
    echo "  status  - 查看程序状态"
    echo "  logs    - 查看实时日志"
    echo "  build   - 构建程序"
    echo "  clean   - 清理文件"
    echo "  help    - 显示帮助信息"
    echo ""
    echo "配置文件: config.json"
    echo "日志文件: $LOG_FILE"
    echo "PID文件:  $PID_FILE"
}

# 主逻辑
case "${1:-help}" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs
        ;;
    build)
        build
        ;;
    clean)
        clean
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_message $RED "❌ 未知命令: $1"
        echo ""
        show_help
        exit 1
        ;;
esac 