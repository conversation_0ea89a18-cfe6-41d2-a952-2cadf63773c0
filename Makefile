# 统一交易所数据订阅系统 - Makefile

PROGRAM_NAME := exchange-signal
BUILD_DIR := .
CONFIG_FILE := config.json

.PHONY: help build start stop restart status logs clean test

# 默认目标
help:
	@echo "🏗️  统一交易所数据订阅系统"
	@echo ""
	@echo "📋 可用命令:"
	@echo "  make build    - 构建程序"
	@echo "  make start    - 启动程序"
	@echo "  make stop     - 停止程序"
	@echo "  make restart  - 重启程序"
	@echo "  make status   - 查看状态"
	@echo "  make logs     - 查看日志"
	@echo "  make clean    - 清理文件"
	@echo "  make test     - 运行测试"
	@echo ""
	@echo "📊 配置文件: $(CONFIG_FILE)"

# 构建程序
build:
	@echo "🔨 构建程序..."
	go mod tidy
	go build -o $(PROGRAM_NAME) .
	@echo "✅ 构建完成"

# 启动程序
start:
	@./start.sh start

# 停止程序
stop:
	@./start.sh stop

# 重启程序
restart:
	@./start.sh restart

# 查看状态
status:
	@./start.sh status

# 查看日志
logs:
	@./start.sh logs

# 清理文件
clean:
	@./start.sh clean

# 运行测试
test:
	@echo "🧪 运行测试..."
	go test ./...

# 初始化项目
init:
	@echo "🔧 初始化项目..."
	mkdir -p logs data config
	go mod tidy
	@echo "✅ 初始化完成"

# 检查代码格式
fmt:
	@echo "🎨 格式化代码..."
	go fmt ./...
	@echo "✅ 格式化完成"

# 代码检查
vet:
	@echo "🔍 代码检查..."
	go vet ./...
	@echo "✅ 检查完成"

# 完整检查（格式化 + 检查 + 测试 + 构建）
check: fmt vet test build
	@echo "✅ 所有检查通过" 