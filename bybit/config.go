package main

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

type Config struct {
	Exchange       string `json:"exchange"`
	WebSocketURL   string `json:"websocket_url"`
	RestAPIURL     string `json:"rest_api_url"`
	ReconnectDelay string `json:"reconnect_delay"`
	PingInterval   string `json:"ping_interval"`
	LogFile        string `json:"log_file"`
	LogLevel       string `json:"log_level"`

	// 运行时解析的时间字段
	reconnectDelay time.Duration
	pingInterval   time.Duration
}

func loadConfig(filename string) (*Config, error) {
	// 默认配置
	config := &Config{
		Exchange:       "bybit",
		WebSocketURL:   "wss://stream.bybit.com/v5/public/linear",
		RestAPIURL:     "https://api.bybit.com",
		ReconnectDelay: "5s",
		PingInterval:   "20s",
		LogFile:        "logs/bybit.log",
		LogLevel:       "info",
	}

	// 尝试加载配置文件
	if _, err := os.Stat(filename); err == nil {
		data, err := os.ReadFile(filename)
		if err != nil {
			return nil, fmt.Errorf("读取配置文件失败: %v", err)
		}

		if err := json.Unmarshal(data, config); err != nil {
			return nil, fmt.Errorf("解析配置文件失败: %v", err)
		}
	}

	// 解析时间字段
	if err := config.parseDurations(); err != nil {
		return nil, fmt.Errorf("解析时间配置失败: %v", err)
	}

	return config, nil
}

func (c *Config) parseDurations() error {
	var err error

	c.reconnectDelay, err = time.ParseDuration(c.ReconnectDelay)
	if err != nil {
		return fmt.Errorf("解析reconnect_delay失败: %v", err)
	}

	c.pingInterval, err = time.ParseDuration(c.PingInterval)
	if err != nil {
		return fmt.Errorf("解析ping_interval失败: %v", err)
	}

	return nil
}

func (c *Config) GetReconnectDelay() time.Duration {
	return c.reconnectDelay
}

func (c *Config) GetPingInterval() time.Duration {
	return c.pingInterval
}

func (c *Config) setDurationStrings() {
	c.ReconnectDelay = c.reconnectDelay.String()
	c.PingInterval = c.pingInterval.String()
}

func (c *Config) saveConfig(filename string) error {
	// 设置字符串字段用于JSON序列化
	c.setDurationStrings()

	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	return nil
}
