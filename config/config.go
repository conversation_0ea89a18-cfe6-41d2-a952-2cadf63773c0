package config

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"
)

// HeartbeatType 心跳类型
type HeartbeatType string

const (
	HeartbeatWebSocketPing HeartbeatType = "websocket_ping" // Binance, Hyperliquid
	HeartbeatTextPing      HeartbeatType = "text_ping"      // OKX, Bitget
	HeartbeatJSONPing      HeartbeatType = "json_ping"      // Gate.io, Bybit
)

// ReconnectStrategy 重连策略
type ReconnectStrategy string

const (
	ReconnectStrategyLimited     ReconnectStrategy = "limited"
	ReconnectStrategyInfinite    ReconnectStrategy = "infinite"
	ReconnectStrategyExponential ReconnectStrategy = "exponential"
)

// ExchangeConfig 交易所配置
type ExchangeConfig struct {
	Enabled              bool              `json:"enabled"`
	APIBaseURL           string            `json:"api_base_url"`
	WSBaseURL            string            `json:"ws_base_url"`
	BatchSize            int               `json:"batch_size"`
	MaxReconnectAttempts int               `json:"max_reconnect_attempts"`
	ReconnectDelay       string            `json:"reconnect_delay"`
	ReconnectStrategy    ReconnectStrategy `json:"reconnect_strategy"`
	MaxReconnectDelay    string            `json:"max_reconnect_delay"`
	PingInterval         string            `json:"ping_interval"`
	PongTimeout          string            `json:"pong_timeout"`
	ConnectionTimeout    string            `json:"connection_timeout"`
	ReadTimeout          string            `json:"read_timeout"`
	WriteTimeout         string            `json:"write_timeout"`
	HeartbeatType        HeartbeatType     `json:"heartbeat_type"`

	// 数据订阅控制
	EnableTradeData     bool     `json:"enable_trade_data"`
	EnableOrderbookData bool     `json:"enable_orderbook_data"`
	EnableDepthData     bool     `json:"enable_depth_data"`
	EnableFundingData   bool     `json:"enable_funding_data"`
	SymbolFilter        []string `json:"symbol_filter"`

	// 解析后的时间配置（不在JSON中）
	reconnectDelayDuration    time.Duration `json:"-"`
	maxReconnectDelayDuration time.Duration `json:"-"`
	pingIntervalDuration      time.Duration `json:"-"`
	pongTimeoutDuration       time.Duration `json:"-"`
	connectionTimeoutDuration time.Duration `json:"-"`
	readTimeoutDuration       time.Duration `json:"-"`
	writeTimeoutDuration      time.Duration `json:"-"`
}

// GetReconnectDelay 获取重连延迟
func (ec *ExchangeConfig) GetReconnectDelay() time.Duration {
	return ec.reconnectDelayDuration
}

// GetMaxReconnectDelay 获取最大重连延迟
func (ec *ExchangeConfig) GetMaxReconnectDelay() time.Duration {
	return ec.maxReconnectDelayDuration
}

// GetPingInterval 获取心跳间隔
func (ec *ExchangeConfig) GetPingInterval() time.Duration {
	return ec.pingIntervalDuration
}

// GetPongTimeout 获取心跳超时
func (ec *ExchangeConfig) GetPongTimeout() time.Duration {
	return ec.pongTimeoutDuration
}

// GetConnectionTimeout 获取连接超时
func (ec *ExchangeConfig) GetConnectionTimeout() time.Duration {
	return ec.connectionTimeoutDuration
}

// GetReadTimeout 获取读取超时
func (ec *ExchangeConfig) GetReadTimeout() time.Duration {
	return ec.readTimeoutDuration
}

// GetWriteTimeout 获取写入超时
func (ec *ExchangeConfig) GetWriteTimeout() time.Duration {
	return ec.writeTimeoutDuration
}

// Config 统一配置
type Config struct {
	// 全局配置
	LogLevel    string `json:"log_level"`
	DataHandler string `json:"data_handler"` // "console", "json", "kafka"
	LogFile     string `json:"log_file"`

	// 统计和监控
	StatsInterval   string `json:"stats_interval"`
	MonitorInterval string `json:"monitor_interval"`

	// 交易所配置
	Exchanges map[string]*ExchangeConfig `json:"exchanges"`

	// 解析后的时间配置（不在JSON中）
	statsIntervalDuration   time.Duration `json:"-"`
	monitorIntervalDuration time.Duration `json:"-"`
}

// GetStatsInterval 获取统计间隔
func (c *Config) GetStatsInterval() time.Duration {
	return c.statsIntervalDuration
}

// GetMonitorInterval 获取监控间隔
func (c *Config) GetMonitorInterval() time.Duration {
	return c.monitorIntervalDuration
}

// DefaultConfig 创建默认配置
func DefaultConfig() *Config {
	config := &Config{
		LogLevel:        "info",
		DataHandler:     "console",
		LogFile:         "logs/exchange_signal.log",
		StatsInterval:   "30s",
		MonitorInterval: "10s",
		Exchanges: map[string]*ExchangeConfig{
			"binance": {
				Enabled:              true,
				APIBaseURL:           "https://fapi.binance.com",
				WSBaseURL:            "wss://fstream.binance.com/ws/",
				BatchSize:            20,
				MaxReconnectAttempts: 10,
				ReconnectDelay:       "5s",
				ReconnectStrategy:    ReconnectStrategyInfinite,
				MaxReconnectDelay:    "5m",
				PingInterval:         "25s",
				PongTimeout:          "150s",
				ConnectionTimeout:    "10s",
				ReadTimeout:          "60s",
				WriteTimeout:         "10s",
				HeartbeatType:        HeartbeatWebSocketPing,
				EnableTradeData:      true,
				EnableOrderbookData:  true,
				EnableDepthData:      false,
				EnableFundingData:    true,
				SymbolFilter:         []string{},
			},
			"okx": {
				Enabled:              true,
				APIBaseURL:           "https://www.okx.com",
				WSBaseURL:            "wss://ws.okx.com:8443/ws/v5/public",
				BatchSize:            20,
				MaxReconnectAttempts: 10,
				ReconnectDelay:       "5s",
				ReconnectStrategy:    ReconnectStrategyInfinite,
				MaxReconnectDelay:    "5m",
				PingInterval:         "25s",
				PongTimeout:          "150s",
				ConnectionTimeout:    "10s",
				ReadTimeout:          "60s",
				WriteTimeout:         "10s",
				HeartbeatType:        HeartbeatTextPing,
				EnableTradeData:      true,
				EnableOrderbookData:  true,
				EnableDepthData:      false,
				EnableFundingData:    true,
				SymbolFilter:         []string{},
			},
			"gateio": {
				Enabled:              true,
				APIBaseURL:           "https://api.gateio.ws",
				WSBaseURL:            "wss://api.gateio.ws/ws/v4/",
				BatchSize:            30,
				MaxReconnectAttempts: 10,
				ReconnectDelay:       "5s",
				ReconnectStrategy:    ReconnectStrategyInfinite,
				MaxReconnectDelay:    "5m",
				PingInterval:         "25s",
				PongTimeout:          "150s",
				ConnectionTimeout:    "10s",
				ReadTimeout:          "60s",
				WriteTimeout:         "10s",
				HeartbeatType:        HeartbeatJSONPing,
				EnableTradeData:      true,
				EnableOrderbookData:  true,
				EnableDepthData:      false,
				EnableFundingData:    true,
				SymbolFilter:         []string{},
			},
			"bybit": {
				Enabled:              true,
				APIBaseURL:           "https://api.bybit.com",
				WSBaseURL:            "wss://stream.bybit.com/v5/public/linear",
				BatchSize:            15,
				MaxReconnectAttempts: 10,
				ReconnectDelay:       "5s",
				ReconnectStrategy:    ReconnectStrategyInfinite,
				MaxReconnectDelay:    "5m",
				PingInterval:         "25s",
				PongTimeout:          "150s",
				ConnectionTimeout:    "10s",
				ReadTimeout:          "60s",
				WriteTimeout:         "10s",
				HeartbeatType:        HeartbeatJSONPing,
				EnableTradeData:      true,
				EnableOrderbookData:  true,
				EnableDepthData:      false,
				EnableFundingData:    true,
				SymbolFilter:         []string{},
			},
			"bitget": {
				Enabled:              true,
				APIBaseURL:           "https://api.bitget.com",
				WSBaseURL:            "wss://ws.bitget.com/mix/v1/stream",
				BatchSize:            20,
				MaxReconnectAttempts: 10,
				ReconnectDelay:       "5s",
				ReconnectStrategy:    ReconnectStrategyInfinite,
				MaxReconnectDelay:    "5m",
				PingInterval:         "25s",
				PongTimeout:          "150s",
				ConnectionTimeout:    "10s",
				ReadTimeout:          "60s",
				WriteTimeout:         "10s",
				HeartbeatType:        HeartbeatTextPing,
				EnableTradeData:      true,
				EnableOrderbookData:  true,
				EnableDepthData:      false,
				EnableFundingData:    true,
				SymbolFilter:         []string{},
			},
		},
	}

	// 解析时间配置
	config.parseDurations()

	return config
}

// LoadConfig 从文件加载配置
func LoadConfig(filename string) (*Config, error) {
	// 如果文件不存在，创建默认配置文件
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		defaultConfig := DefaultConfig()
		if err := SaveConfig(defaultConfig, filename); err != nil {
			return nil, fmt.Errorf("保存默认配置失败: %v", err)
		}
		return defaultConfig, nil
	}

	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	config := &Config{}
	if err := json.Unmarshal(data, config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 解析时间配置
	if err := config.parseDurations(); err != nil {
		return nil, fmt.Errorf("解析时间配置失败: %v", err)
	}

	return config, nil
}

// SaveConfig 保存配置到文件
func SaveConfig(config *Config, filename string) error {
	// 确保目录存在
	if err := os.MkdirAll(filepath.Dir(filename), 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %v", err)
	}

	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	return nil
}

// parseDurations 解析时间配置
func (c *Config) parseDurations() error {
	var err error

	// 解析全局时间配置
	if c.StatsInterval != "" {
		c.statsIntervalDuration, err = time.ParseDuration(c.StatsInterval)
		if err != nil {
			return fmt.Errorf("解析统计间隔失败: %v", err)
		}
	}

	if c.MonitorInterval != "" {
		c.monitorIntervalDuration, err = time.ParseDuration(c.MonitorInterval)
		if err != nil {
			return fmt.Errorf("解析监控间隔失败: %v", err)
		}
	}

	// 解析各交易所时间配置
	for name, exchangeConfig := range c.Exchanges {
		if err := parseExchangeDurations(exchangeConfig); err != nil {
			return fmt.Errorf("解析交易所 %s 时间配置失败: %v", name, err)
		}
	}

	return nil
}

// parseExchangeDurations 解析交易所时间配置
func parseExchangeDurations(ec *ExchangeConfig) error {
	var err error

	if ec.ReconnectDelay != "" {
		ec.reconnectDelayDuration, err = time.ParseDuration(ec.ReconnectDelay)
		if err != nil {
			return fmt.Errorf("解析重连延迟失败: %v", err)
		}
	}

	if ec.MaxReconnectDelay != "" {
		ec.maxReconnectDelayDuration, err = time.ParseDuration(ec.MaxReconnectDelay)
		if err != nil {
			return fmt.Errorf("解析最大重连延迟失败: %v", err)
		}
	}

	if ec.PingInterval != "" {
		ec.pingIntervalDuration, err = time.ParseDuration(ec.PingInterval)
		if err != nil {
			return fmt.Errorf("解析心跳间隔失败: %v", err)
		}
	}

	if ec.PongTimeout != "" {
		ec.pongTimeoutDuration, err = time.ParseDuration(ec.PongTimeout)
		if err != nil {
			return fmt.Errorf("解析心跳超时失败: %v", err)
		}
	}

	if ec.ConnectionTimeout != "" {
		ec.connectionTimeoutDuration, err = time.ParseDuration(ec.ConnectionTimeout)
		if err != nil {
			return fmt.Errorf("解析连接超时失败: %v", err)
		}
	}

	if ec.ReadTimeout != "" {
		ec.readTimeoutDuration, err = time.ParseDuration(ec.ReadTimeout)
		if err != nil {
			return fmt.Errorf("解析读取超时失败: %v", err)
		}
	}

	if ec.WriteTimeout != "" {
		ec.writeTimeoutDuration, err = time.ParseDuration(ec.WriteTimeout)
		if err != nil {
			return fmt.Errorf("解析写入超时失败: %v", err)
		}
	}

	return nil
}
