package main

import (
	"encoding/json"
	"fmt"

	"github.com/sirupsen/logrus"
)

// DepthData 深度数据结构（多档买卖价格）
type DepthData struct {
	LastUpdateID int64      `json:"lastUpdateId"`
	Symbol       string     `json:"s"`
	Bids         [][]string `json:"bids"` // 买单深度 [价格, 数量]
	Asks         [][]string `json:"asks"` // 卖单深度 [价格, 数量]
}

// DataHandler 数据处理器接口
type DataHandler interface {
	HandleTradeData(data *TradeData)           // 处理交易数据
	HandleBookTickerData(data *BookTickerData) // 处理盘口数据（最优买卖价）
	HandleDepthData(data *DepthData)           // 处理深度数据（多档买卖价）
}

// DefaultDataHandler 默认数据处理器
type DefaultDataHandler struct {
	logger *logrus.Logger
}

// NewDefaultDataHandler 创建默认数据处理器
func NewDefaultDataHandler(logger *logrus.Logger) *DefaultDataHandler {
	return &DefaultDataHandler{
		logger: logger,
	}
}

// HandleTradeData 处理交易数据
func (h *DefaultDataHandler) HandleTradeData(data *TradeData) {
	// 只处理数据，不输出详细日志
}

// HandleBookTickerData 处理盘口数据（最优买卖价格）
func (h *DefaultDataHandler) HandleBookTickerData(data *BookTickerData) {
	// 只处理数据，不输出详细日志
}

// HandleDepthData 处理深度数据（多档买卖价格）
func (h *DefaultDataHandler) HandleDepthData(data *DepthData) {
	// 只处理数据，不输出详细日志
}

// getBestPrice 获取最优价格
func getBestPrice(levels [][]string, isBid bool) string {
	if len(levels) == 0 {
		return "N/A"
	}
	return levels[0][0] // 第一档价格
}

// FileDataHandler 文件数据处理器（将数据写入文件）
type FileDataHandler struct {
	logger     *logrus.Logger
	tradeFile  string
	tickerFile string
	depthFile  string
}

// NewFileDataHandler 创建文件数据处理器
func NewFileDataHandler(logger *logrus.Logger, tradeFile, tickerFile, depthFile string) *FileDataHandler {
	return &FileDataHandler{
		logger:     logger,
		tradeFile:  tradeFile,
		tickerFile: tickerFile,
		depthFile:  depthFile,
	}
}

// HandleTradeData 处理交易数据并写入文件
func (h *FileDataHandler) HandleTradeData(data *TradeData) {
	// 这里可以实现将数据写入文件的逻辑
	jsonData, _ := json.Marshal(data)
	h.logger.Debugf("📈 交易数据写入文件 %s: %s", h.tradeFile, string(jsonData))
}

// HandleBookTickerData 处理盘口数据并写入文件
func (h *FileDataHandler) HandleBookTickerData(data *BookTickerData) {
	// 这里可以实现将数据写入文件的逻辑
	jsonData, _ := json.Marshal(data)
	h.logger.Debugf("📊 盘口数据写入文件 %s: %s", h.tickerFile, string(jsonData))
}

// HandleDepthData 处理深度数据并写入文件
func (h *FileDataHandler) HandleDepthData(data *DepthData) {
	jsonData, _ := json.Marshal(data)
	h.logger.Debugf("📋 深度数据写入文件 %s: %s", h.depthFile, string(jsonData))
}

// DatabaseDataHandler 数据库数据处理器（将数据写入数据库）
type DatabaseDataHandler struct {
	logger *logrus.Logger
	// 这里可以添加数据库连接
}

// NewDatabaseDataHandler 创建数据库数据处理器
func NewDatabaseDataHandler(logger *logrus.Logger) *DatabaseDataHandler {
	return &DatabaseDataHandler{
		logger: logger,
	}
}

// HandleTradeData 处理交易数据并写入数据库
func (h *DatabaseDataHandler) HandleTradeData(data *TradeData) {
	// 这里可以实现将数据写入数据库的逻辑
	h.logger.Debugf("📈 交易数据写入数据库: %+v", data)
}

// HandleBookTickerData 处理盘口数据并写入数据库
func (h *DatabaseDataHandler) HandleBookTickerData(data *BookTickerData) {
	// 这里可以实现将数据写入数据库的逻辑
	h.logger.Debugf("📊 盘口数据写入数据库: %+v", data)
}

// HandleDepthData 处理深度数据并写入数据库
func (h *DatabaseDataHandler) HandleDepthData(data *DepthData) {
	// 这里可以实现将数据写入数据库的逻辑
	h.logger.Debugf("📋 深度数据写入数据库: %+v", data)
}

// parseTradeData 解析交易数据
func parseTradeData(message []byte) (*TradeData, error) {
	var data TradeData
	if err := json.Unmarshal(message, &data); err != nil {
		return nil, fmt.Errorf("解析交易数据失败: %v", err)
	}
	return &data, nil
}

// parseBookTickerData 解析盘口数据
func parseBookTickerData(message []byte) (*BookTickerData, error) {
	var data BookTickerData
	if err := json.Unmarshal(message, &data); err != nil {
		return nil, fmt.Errorf("解析盘口数据失败: %v", err)
	}
	return &data, nil
}

// parseDepthData 解析深度数据
func parseDepthData(message []byte) (*DepthData, error) {
	var data DepthData
	if err := json.Unmarshal(message, &data); err != nil {
		return nil, fmt.Errorf("解析深度数据失败: %v", err)
	}
	return &data, nil
}
