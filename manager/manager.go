package manager

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"

	"exchange_signal/config"
	"exchange_signal/data"
	"exchange_signal/exchange"
	"exchange_signal/exchange/binance"
	"exchange_signal/exchange/bitget"
	"exchange_signal/exchange/bybit"
	"exchange_signal/exchange/gateio"
	"exchange_signal/exchange/okx"
)

// ExchangeManager 交易所管理器
type ExchangeManager struct {
	config      *config.Config
	logger      *logrus.Logger
	dataHandler data.DataHandler
	exchanges   map[string]exchange.ExchangeClient
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup

	// 统计信息
	totalStats  *data.GlobalStats
	statsLogger *time.Ticker

	// 状态监控
	statusMonitor *time.Ticker
}

// NewExchangeManager 创建交易所管理器
func NewExchangeManager(cfg *config.Config) (*ExchangeManager, error) {
	// 创建logger
	logger := logrus.New()
	level, err := logrus.ParseLevel(cfg.LogLevel)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)
	logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
		ForceColors:   true,
	})

	// 创建数据处理器
	var dataHandler data.DataHandler
	switch cfg.DataHandler {
	case "console":
		dataHandler = data.NewConsoleDataHandler(logger)
	case "json":
		dataHandler = data.NewJSONDataHandler(logger, "data/output.json")
	default:
		dataHandler = data.NewConsoleDataHandler(logger)
	}

	ctx, cancel := context.WithCancel(context.Background())

	manager := &ExchangeManager{
		config:      cfg,
		logger:      logger,
		dataHandler: dataHandler,
		exchanges:   make(map[string]exchange.ExchangeClient),
		ctx:         ctx,
		cancel:      cancel,
		totalStats: &data.GlobalStats{
			ExchangeStats:  make(map[string]*data.DataStats),
			StartTime:      time.Now(),
			LastUpdateTime: time.Now(),
		},
	}

	// 初始化所有启用的交易所
	if err := manager.initExchanges(); err != nil {
		return nil, fmt.Errorf("初始化交易所失败: %v", err)
	}

	return manager, nil
}

// initExchanges 初始化交易所客户端
func (em *ExchangeManager) initExchanges() error {
	for name, cfg := range em.config.Exchanges {
		if !cfg.Enabled {
			em.logger.Infof("🔄 跳过未启用的交易所: %s", name)
			continue
		}

		client, err := em.createExchangeClient(name)
		if err != nil {
			em.logger.Errorf("❌ 创建交易所客户端失败 %s: %v", name, err)
			continue
		}

		// 设置配置和数据处理器
		client.SetConfig(cfg)
		client.SetDataHandler(em.dataHandler)

		em.exchanges[name] = client
		em.logger.Infof("✅ 初始化交易所客户端成功: %s", name)
	}

	if len(em.exchanges) == 0 {
		return fmt.Errorf("没有可用的交易所客户端")
	}

	return nil
}

// createExchangeClient 创建具体的交易所客户端
func (em *ExchangeManager) createExchangeClient(name string) (exchange.ExchangeClient, error) {
	switch name {
	case "binance":
		return binance.NewBinanceClient(), nil
	case "okx":
		return okx.NewOKXClient(), nil
	case "gateio":
		return gateio.NewGateIOClient(), nil
	case "bybit":
		return bybit.NewBybitClient(), nil
	case "bitget":
		return bitget.NewBitgetClient(), nil

	default:
		return nil, fmt.Errorf("不支持的交易所: %s", name)
	}
}

// Start 启动所有交易所
func (em *ExchangeManager) Start() error {
	em.logger.Info("🚀 启动交易所数据订阅系统...")

	// 启动各个交易所
	for name, client := range em.exchanges {
		em.wg.Add(1)
		go func(exchangeName string, exchangeClient exchange.ExchangeClient) {
			defer em.wg.Done()

			em.logger.Infof("📈 启动交易所: %s", exchangeName)

			if err := exchangeClient.Start(); err != nil {
				em.logger.Errorf("❌ 启动交易所失败 %s: %v", exchangeName, err)
				return
			}

			em.logger.Infof("✅ 交易所启动成功: %s", exchangeName)
		}(name, client)
	}

	// 启动统计日志
	em.startStatsLogger()

	// 启动状态监控
	em.startStatusMonitor()

	em.logger.Info("✅ 所有交易所启动完成")
	return nil
}

// Stop 停止所有交易所
func (em *ExchangeManager) Stop() error {
	em.logger.Info("🛑 停止交易所数据订阅系统...")

	// 停止统计日志和状态监控
	if em.statsLogger != nil {
		em.statsLogger.Stop()
	}
	if em.statusMonitor != nil {
		em.statusMonitor.Stop()
	}

	// 停止所有交易所
	for name, client := range em.exchanges {
		em.logger.Infof("🛑 停止交易所: %s", name)
		if err := client.Stop(); err != nil {
			em.logger.Errorf("❌ 停止交易所失败 %s: %v", name, err)
		}
	}

	// 取消上下文
	em.cancel()

	// 等待所有goroutine结束
	em.wg.Wait()

	// 关闭数据处理器
	if err := em.dataHandler.Close(); err != nil {
		em.logger.Errorf("❌ 关闭数据处理器失败: %v", err)
	}

	em.logger.Info("✅ 所有交易所停止完成")
	return nil
}

// startStatsLogger 启动统计日志
func (em *ExchangeManager) startStatsLogger() {
	interval := em.config.GetStatsInterval()
	if interval == 0 {
		interval = 30 * time.Second
	}

	em.statsLogger = time.NewTicker(interval)

	em.wg.Add(1)
	go func() {
		defer em.wg.Done()

		for {
			select {
			case <-em.ctx.Done():
				return
			case <-em.statsLogger.C:
				em.printStats()
			}
		}
	}()
}

// startStatusMonitor 启动状态监控
func (em *ExchangeManager) startStatusMonitor() {
	interval := em.config.GetMonitorInterval()
	if interval == 0 {
		interval = 10 * time.Second
	}

	em.statusMonitor = time.NewTicker(interval)

	em.wg.Add(1)
	go func() {
		defer em.wg.Done()

		for {
			select {
			case <-em.ctx.Done():
				return
			case <-em.statusMonitor.C:
				em.monitorStatus()
			}
		}
	}()
}

// printStats 打印统计信息
func (em *ExchangeManager) printStats() {
	stats := em.dataHandler.GetStats()

	em.logger.Infof("📊 数据统计 - 总计: 交易=%d, 盘口=%d, 深度=%d, 资金费率=%d",
		stats.TotalTradeCount,
		stats.TotalOrderbookCount,
		stats.TotalDepthCount,
		stats.TotalFundingCount)

	// 打印各交易所统计
	for exchange, stat := range stats.ExchangeStats {
		em.logger.Infof("📈 [%s] 交易=%d, 盘口=%d, 深度=%d, 资金费率=%d",
			exchange,
			stat.TradeCount,
			stat.OrderbookCount,
			stat.DepthCount,
			stat.FundingCount)
	}
}

// monitorStatus 监控交易所状态
func (em *ExchangeManager) monitorStatus() {
	for name, client := range em.exchanges {
		status := client.GetStatus()

		// 检查连接状态
		if status.State == "disconnected" {
			em.logger.Warnf("⚠️ [%s] 连接断开", name)
		} else if status.State == "connecting" {
			em.logger.Infof("🔄 [%s] 正在连接...", name)
		}

		// 检查心跳延迟
		if status.AvgPongDelay > 5*time.Second {
			em.logger.Warnf("⚠️ [%s] 心跳延迟过高: %v", name, status.AvgPongDelay)
		}

		// 检查连接质量（暂时禁用，避免误报）
		if status.ConnectionQuality < 0.1 && status.ConnectionQuality > 0 {
			em.logger.Warnf("⚠️ [%s] 连接质量较差: %.2f", name, status.ConnectionQuality)
		}
	}
}

// GetStatus 获取所有交易所状态
func (em *ExchangeManager) GetStatus() map[string]*data.ConnectionStatus {
	status := make(map[string]*data.ConnectionStatus)

	for name, client := range em.exchanges {
		status[name] = client.GetStatus()
	}

	return status
}

// GetStats 获取全局统计信息
func (em *ExchangeManager) GetStats() *data.GlobalStats {
	return em.dataHandler.GetStats()
}

// GetExchanges 获取所有交易所客户端
func (em *ExchangeManager) GetExchanges() map[string]exchange.ExchangeClient {
	return em.exchanges
}
